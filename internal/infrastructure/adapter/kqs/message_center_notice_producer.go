package kqs

import (
	"context"
	"github.com/zeromicro/go-zero/core/logc"
	"nebula/internal/infrastructure/adapter/addons"
	"nebula/internal/utils"
	"time"
)

// MessageCenterNoticeProducerInterface 消息中心通知生产者接口
type MessageCenterNoticeProducerInterface interface {
	// SendNormalMessage 发送普通消息 (Level 1)
	SendNormalMessage(ctx context.Context, message Message) error

	// SendWarningMessage 发送预警消息 (Level 2)
	SendWarningMessage(ctx context.Context, message Message) error

	// SendUrgentMessage 发送紧急消息 (Level 3)
	SendUrgentMessage(ctx context.Context, message Message) error

	// Close 关闭生产者
	Close() error
}

type Message struct {
	BusinessID   string
	BusinessType string
	Content      string
	RecipientID  string
}

// MessageCenterNoticeProducer 专用于消息中心通知的 Kafka 生产者
type MessageCenterNoticeProducer struct {
	KafkaProducer     *KafkaProducer
	idGeneratorAddons addons.IDGeneratorAddons
}

// NewMessageCenterNoticeProducer 创建消息中心通知专用Producer实例
// @param brokers Kafka broker地址
// @param topic Kafka topic
// @return *MessageCenterNoticeProducer 消息中心通知专用Producer实例
func NewMessageCenterNoticeProducer(idGeneratorAddons addons.IDGeneratorAddons, brokers []string, topic string) *MessageCenterNoticeProducer {
	return &MessageCenterNoticeProducer{
		KafkaProducer:     NewKafkaProducer(brokers, topic),
		idGeneratorAddons: idGeneratorAddons,
	}
}

// Close 关闭生产者
func (p *MessageCenterNoticeProducer) Close() error {
	return p.KafkaProducer.Close()
}

// SendNormalMessage 发送普通消息 (Level 1)
// 参数: recipientID: 接收人ID | content: 消息内容 | moduleName: 模块名称 | businessType: 业务类型 | businessID: 业务ID
func (p *MessageCenterNoticeProducer) SendNormalMessage(ctx context.Context, message Message) error {
	return p.SendCustomLevelMessage(ctx, message, 1)
}

// SendWarningMessage 发送预警消息 (Level 2)
// 参数: recipientID: 接收人ID | content: 消息内容 | moduleName: 模块名称 | businessType: 业务类型 | businessID: 业务ID
func (p *MessageCenterNoticeProducer) SendWarningMessage(ctx context.Context, message Message) error {
	return p.SendCustomLevelMessage(ctx, message, 2)
}

// SendUrgentMessage 发送紧急消息 (Level 3)
// 参数: recipientID: 接收人ID | content: 消息内容 | moduleName: 模块名称 | businessType: 业务类型 | businessID: 业务ID
func (p *MessageCenterNoticeProducer) SendUrgentMessage(ctx context.Context, message Message) error {
	return p.SendCustomLevelMessage(ctx, message, 3)
}

// SendCustomLevelMessage 发送自定义级别消息
// 参数 level: 1-普通（默认），2-预警，3-紧急 | recipientID: 接收人ID | content: 消息内容 | moduleName: 模块名称 | businessType: 业务类型 | businessID: 业务ID
func (p *MessageCenterNoticeProducer) SendCustomLevelMessage(ctx context.Context, message Message, level int) error {
	// 获取用户登录信息
	userLoginInfo := utils.GetCurrentLoginUser(ctx)

	// 封装消息
	messageInfo := MessageCenterNotice{
		ID:             p.idGeneratorAddons.GenerateIDString(),
		TenantID:       userLoginInfo.TenantId,
		OrganizationID: userLoginInfo.OrganizationId,
		BusinessID:     message.BusinessID,
		BusinessType:   message.BusinessType,
		ModuleName:     getModuleName(message.BusinessType),
		Content:        message.Content,
		CreatedAt:      time.Now().UnixMilli(),
		RecipientID:    message.RecipientID,
		Level:          level,
	}

	// 发送消息
	err := p.KafkaProducer.SendMessage(ctx, "message_center_notice", messageInfo)
	if err != nil {
		logc.Errorf(ctx, "发送消息中心通知失败, level: %d, recipientID: %s, err: %v", level, message.RecipientID, err)
		return err
	}

	return nil
}

// 根据业务类型获取模块名称
func getModuleName(businessType string) string {
	switch businessType {
	case "document_library_distribute":
		return "体系管理-文件管理"
	default:
		return "未知模块"
	}
}

// MessageCenterNotice 消息中心通知消息结构
type MessageCenterNotice struct {
	ID             string `json:"id"`              // 消息唯一标识，用于排错、保证幂等性
	TenantID       string `json:"tenant_id"`       // 租户 id
	OrganizationID string `json:"organization_id"` // 组织架构 id
	BusinessID     string `json:"business_id"`     // 业务 id 匹配
	BusinessType   string `json:"business_type"`   // 业务类型
	ModuleName     string `json:"module_name"`     // 模块名称
	Content        string `json:"content"`         // 通知内容
	CreatedAt      int64  `json:"created_at"`      // 发起时间，毫秒级时间戳
	RecipientID    string `json:"recipient_id"`    // 接收人 id
	Level          int    `json:"level"`           // 1-普通（默认），2-预警，3-紧急
}

package mapper

import (
	"context"

	"gorm.io/gorm"
)

// OrganizationClosure 部门闭包表模型
// 功能：记录部门之间的层级关系，数据由触发器自动维护
type OrganizationClosure struct {
	// AncestorID 祖先部门ID
	AncestorID string `gorm:"column:ancestor_id;type:varchar(64);primaryKey;not null;comment:祖先部门ID" json:"ancestor_id"`
	// DescendantID 后代部门ID
	DescendantID string `gorm:"column:descendant_id;type:varchar(64);primaryKey;not null;comment:后代部门ID" json:"descendant_id"`
	// Depth 层级深度
	Depth *uint8 `gorm:"column:depth;type:tinyint unsigned;comment:层级深度" json:"depth"`
	// DescendantNodeType 后代部门类型
	DescendantNodeType *int8 `gorm:"column:descendant_node_type;type:tinyint;comment:后代部门类型" json:"descendant_node_type"`
	// AncestorNodeType 祖先部门类型
	AncestorNodeType *int8 `gorm:"column:ancestor_node_type;type:tinyint;comment:祖先部门类型" json:"ancestor_node_type"`
}

// TableName 指定表名
// 功能：返回数据库表名
// 返回值：
//   - string: 表名
func (OrganizationClosure) TableName() string {
	return "organization_closure"
}

// OrganizationClosureClient 部门闭包表客户端
// 功能：提供部门闭包表的数据库操作方法
type OrganizationClosureClient struct {
	DB *PhoenixDB
}

// NewOrganizationClosureClient 创建部门闭包表客户端实例
// 功能：初始化部门闭包表客户端
// 参数：
//   - db: GORM数据库连接实例
//
// 返回值：
//   - *OrganizationClosureClient: 部门闭包表客户端实例
func NewOrganizationClosureClient(DB *PhoenixDB) *OrganizationClosureClient {
	return &OrganizationClosureClient{DB: DB}
}

// GetDescendantsByAncestorID 根据祖先部门ID查询所有后代部门
// 功能：查询指定祖先部门下的所有后代部门信息
// 实现逻辑：
//  1. 构建查询条件，按祖先部门ID过滤
//  2. 按深度升序排列，确保层级关系清晰
//  3. 执行查询并返回结果
//
// 参数：
//   - ctx: 上下文
//   - ancestorID: 祖先部门ID
//
// 返回值：
//   - []OrganizationClosure: 后代部门闭包关系列表
//   - error: 错误信息
func (c *OrganizationClosureClient) GetDescendantsByAncestorID(ctx context.Context, ancestorID string) ([]OrganizationClosure, error) {
	// 1. 参数验证
	if ancestorID == "" {
		return nil, gorm.ErrRecordNotFound
	}

	// 2. 构建查询
	var results []OrganizationClosure
	query := c.DB.db.WithContext(ctx).
		Where("ancestor_id = ?", ancestorID).
		Order("depth ASC, descendant_id ASC")

	// 3. 执行查询
	err := query.Find(&results).Error
	if err != nil {
		return nil, err
	}

	return results, nil
}

// 获取集团 id
func (c *OrganizationClosureClient) GetGroupID(ctx context.Context, orgID string) (string, error) {
	var groupID string
	err := c.DB.db.WithContext(ctx).
		Table("organization_closure").
		Select("ancestor_id").
		Where("descendant_id = ? AND ancestor_node_type = 0", orgID).Limit(1).
		Find(&groupID).Error
	return groupID, err
}

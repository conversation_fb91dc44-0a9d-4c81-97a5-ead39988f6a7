package mapper

import (
	"context"
	"time"

	"gorm.io/gorm"
)

const TableNameSaasRole = "saas_role"

type SaasRoleClient struct {
	db *gorm.DB `name:"saas_db"`
}

func NewSaasRoleClient(db *PhoenixDB) *SaasRoleClient {
	return &SaasRoleClient{
		db: db.GetDB(),
	}
}

// 根据组织 id 判断用户是否包含角色
func (c *SaasRoleClient) CheckUserHasRoleCode(ctx context.Context, userId string, organizationId string, roleCodes ...string) (bool, error) {
	var count int64
	err := c.db.WithContext(ctx).Table("saas_role").
		Select("count(*)").
		Where("id IN (SELECT role_id FROM role_users WHERE user_id = ?)", userId).
		Where("code in (?) AND organization_id = ? AND status = 1 AND deleted_at IS NULL", roleCodes, organizationId).
		Count(&count).Error
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

// SaasRole mapped from table <saas_role>
type SaasRole struct {
	ID             string         `gorm:"column:id;primaryKey;comment:Snowflake ID | 全局唯一ID" json:"id"`                                                     // Snowflake ID | 全局唯一ID
	CreatedAt      time.Time      `gorm:"column:created_at;comment:Created Time | 创建时间" json:"created_at"`                                                  // Created Time | 创建时间
	UpdatedAt      time.Time      `gorm:"column:updated_at;comment:Updated Time | 更新时间" json:"updated_at"`                                                  // Updated Time | 更新时间
	Status         bool           `gorm:"column:status;default:1;comment:status true normal false ban | 状态  正常/禁用" json:"status"`                           // status true normal false ban | 状态  正常/禁用
	Sort           int32          `gorm:"column:sort;not null;default:1;comment:Sort number | 排序编号" json:"sort"`                                            // Sort number | 排序编号
	DeletedAt      gorm.DeletedAt `gorm:"column:deleted_at;comment:Deleted Time | 删除时间（软删除标识）" json:"deleted_at"`                                           // Deleted Time | 删除时间（软删除标识）
	Name           string         `gorm:"column:name;not null;comment:role name | 角色名" json:"name"`                                                         // role name | 角色名
	Code           string         `gorm:"column:code;not null;comment:role code for permission control in front end | 角色码，用于前端权限控制" json:"code"`            // role code for permission control in front end | 角色码，用于前端权限控制
	UID            string         `gorm:"column:uid;not null;comment:UUID" json:"uid"`                                                                      // UUID
	DefaultRouter  string         `gorm:"column:default_router;not null;default:dashboard;comment:default menu : dashboard | 默认登录页面" json:"default_router"` // default menu : dashboard | 默认登录页面
	Remark         string         `gorm:"column:remark;not null;comment:remark | 备注" json:"remark"`                                                         // remark | 备注
	TenantID       string         `gorm:"column:tenant_id;not null;comment:Tenant ID" json:"tenant_id"`                                                     // Tenant ID
	ParentID       string         `gorm:"column:parent_id;comment:Parent role ID | 父级角色ID" json:"parent_id"`                                                // Parent role ID | 父级角色ID
	OrganizationID string         `gorm:"column:organization_id;not null;comment:Organization ID" json:"organization_id"`                                   // Organization ID
}

// TableName SaasRole's table name
func (*SaasRole) TableName() string {
	return TableNameSaasRole
}

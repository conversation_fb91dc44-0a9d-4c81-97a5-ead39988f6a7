package workflow

import (
	"context"
	"nebula/internal/consts"
	"nebula/internal/infrastructure/adapter/grpc/pb/docvault"
	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/svc"

	"github.com/bytedance/sonic"
	"github.com/zeromicro/go-zero/core/logc"
)

// MessageCenterReadReceiptConsumer 消息中心已读回执消费者
// 功能: 处理消息中心的已读回执消息，根据不同业务类型执行相应的业务逻辑
// 支持的业务类型: 文档库发放、签名审批、内部文档、外部文档、图书管理等
type MessageCenterReadReceiptConsumer struct {
	svcCtx *svc.ServiceContext
}

// NewMessageCenterReadReceiptConsumer 创建消息中心已读回执消费者实例
// 参数: svcCtx - 服务上下文
// 返回值: *MessageCenterReadReceiptConsumer - 消费者实例
func NewMessageCenterReadReceiptConsumer(svcCtx *svc.ServiceContext) *MessageCenterReadReceiptConsumer {
	return &MessageCenterReadReceiptConsumer{
		svcCtx: svcCtx,
	}
}

// Handle 处理消息中心已读回执消息
// 功能: 解析消息并根据业务类型分发到对应的处理方法
// 参数: ctx - 上下文，message - 消息内容
// 返回值: error - 处理错误
func (h *MessageCenterReadReceiptConsumer) Handle(ctx context.Context, message []byte) error {
	//解析消息内容
	var msg MessageCenterReadReceiptMessage
	if err := sonic.Unmarshal(message, &msg); err != nil {
		logc.Errorf(ctx, "解析消息中心已读回执消息失败: %v", err)
		return err
	}

	// 根据业务类型分发处理
	if err := h.dispatchByBusinessType(ctx, msg); err != nil {
		logc.Errorf(ctx, "处理消息中心已读回执失败，业务类型: %s, 业务ID: %s, 错误: %v",
			msg.BusinessType, msg.BusinessID, err)
		return err
	}

	return nil
}

// Name 返回消费者名称
// 返回值: string - 消费者名称
func (h *MessageCenterReadReceiptConsumer) Name() string {
	return "message_center_notice_read_callback"
}

// dispatchByBusinessType 根据业务类型分发处理
// 功能: 根据消息的业务类型调用对应的处理方法
// 参数: ctx - 上下文，msg - 已读回执消息
// 返回值: error - 处理错误
func (h *MessageCenterReadReceiptConsumer) dispatchByBusinessType(ctx context.Context, msg MessageCenterReadReceiptMessage) error {
	switch msg.BusinessType {
	case consts.MessageBusinessTypeDocumentLibraryDistribute:
		// 文档库发放业务
		go h.handleDocumentLibraryDistribute(ctx, msg)
	case consts.MessageBusinessTypeBorrowRecord:
		// 借阅记录业务
		go h.handleBorrowRecord(ctx, msg)
	default:
		logc.Infof(ctx, "未知的业务类型: %s, 跳过处理", msg.BusinessType)
		return nil
	}

	return nil
}

// handleDocumentLibraryDistribute 处理文档库发放业务的已读回执
// 功能: 处理文档库发放相关的已读状态更新，更新签收状态
// 参数: ctx - 上下文，msg - 已读回执消息
// 返回值: error - 处理错误
func (h *MessageCenterReadReceiptConsumer) handleDocumentLibraryDistribute(ctx context.Context, msg MessageCenterReadReceiptMessage) {
	// 使用 BusinessID（储存的是发放列表id）查询发放清单信息
	distributeRecords, err := mapper.NewDistributeRecordFileClient(h.svcCtx.DocvaultDB).FindByRecordID(ctx, msg.BusinessID)
	if err != nil {
		logc.Errorf(ctx, "查询发放清单信息失败: %v", err)
		return
	}

	if len(distributeRecords) == 0 {
		logc.Infof(ctx, "未找到发放清单信息，业务ID: %s", msg.BusinessID)
		return
	}

	// 获取发放清单id列表
	var distributeRecordIDs []string
	for _, distributeRecord := range distributeRecords {
		distributeRecordIDs = append(distributeRecordIDs, distributeRecord.ID)
	}

	// 更新签收状态
	_, err = docvault.NewDocumentLibraryClient(h.svcCtx.DocvaultRpcConn).UpdateSignStatus(ctx, &docvault.UpdateSignStatusReq{
		InventoryId: distributeRecordIDs,
		UserId:      msg.RecipientID,
	})
	if err != nil {
		logc.Errorf(ctx, "更新签收状态失败: %v", err)
		return
	}
}

// handleBorrowRecord 处理借阅记录业务的已读回执
// 功能: 处理借阅记录相关的已读状态更新
// 参数: ctx - 上下文，msg - 已读回执消息
// 返回值: error - 处理错误
func (h *MessageCenterReadReceiptConsumer) handleBorrowRecord(ctx context.Context, msg MessageCenterReadReceiptMessage) {
	// TODO: 实现借阅记录业务的已读回执处理逻辑
	logc.Infof(ctx, "处理借阅记录已读回执，业务ID: %s, 接收人: %s", msg.BusinessID, msg.RecipientID)
}

package workflow

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/zeromicro/go-zero/core/logc"
	"nebula/internal/consts"
	"nebula/internal/infrastructure/adapter/grpc/pb/docvault"
	"nebula/internal/infrastructure/adapter/kqs"
	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/svc"
	"nebula/internal/utils"
)

type DistributeApprovalConsumer struct {
	svcCtx *svc.ServiceContext
}

func NewDistributeApprovalConsumer(svcCtx *svc.ServiceContext) *DistributeApprovalConsumer {
	return &DistributeApprovalConsumer{
		svcCtx: svcCtx,
	}
}

func (h *DistributeApprovalConsumer) Handle(ctx context.Context, message []byte) error {
	var msg WorkflowEventMessage
	err := json.Unmarshal(message, &msg)
	if err != nil {
		logc.Errorf(ctx, "解析Kafka消息失败: %v", err)
		return err
	}

	// 构造用户登录信息信息
	userLoginInfo := utils.UserLoginInfo{
		UserId:         msg.SponsorID,
		TenantId:       msg.TenantID,
		OrganizationId: msg.OrganizationID,
		DeviceKind:     1,
		IsVirtualUser:  false,
	}
	ctx = userLoginInfo.SetContext(ctx)

	h.handleApproved(ctx, msg)
	return nil
}

func (h *DistributeApprovalConsumer) Name() string {
	return "file_grant"
}

// handleApproved 处理审批事件
// 功能: 根据审批事件类型，处理审批事件
// 参数 ctx: 上下文 | msg: 审批事件消息
// 返回值: 无
// 错误类型: 无
func (h *DistributeApprovalConsumer) handleApproved(ctx context.Context, msg WorkflowEventMessage) {
	// 说明
	// WorkflowEventRejected: 如果审批驳回，将状态更新为 4-已驳回
	// WorkflowEventCanceled: 如果审批撤销，将状态更新为 1-待提交
	// WorkflowEventPassed: 如果审批通过，将状态更新为 3-已审批，并保存审批人信息

	switch msg.EventType {
	case consts.WorkflowEventRejected: // 审批驳回
		h.handleRejected(ctx, msg)

	case consts.WorkflowEventCanceled: // 审批撤销
		h.handleCanceled(ctx, msg)

	case consts.WorkflowEventPassed: // 审批通过
		h.handlePassed(ctx, msg)
	default:
		logc.Errorf(ctx, "未知的事件类型: %s", msg.EventType)
	}
}

// handleRejected 处理审批驳回事件
// 功能: 更新发放状态为已驳回
// 参数 ctx: 上下文 | msg: 审批事件消息
// 返回值: 无
// 错误类型: 无
func (h *DistributeApprovalConsumer) handleRejected(ctx context.Context, msg WorkflowEventMessage) {
	// 审批驳回，更新状态为已驳回
	_, err := docvault.NewDocumentLibraryClient(h.svcCtx.DocvaultRpcConn).UpdateDistributeStatusByWorkflowId(ctx, &docvault.UpdateDistributeStatusReq{
		WorkflowId: msg.WorkflowID,
		Status:     4,
	})
	if err != nil {
		logc.Errorf(ctx, "处理审批失败: %v", err)
	}
}

// handleCanceled 处理审批撤销事件
// 功能: 更新发放状态为待提交
// 参数 ctx: 上下文 | msg: 审批事件消息
// 返回值: 无
// 错误类型: 无
func (h *DistributeApprovalConsumer) handleCanceled(ctx context.Context, msg WorkflowEventMessage) {
	// 审批撤销，更新状态为待提交
	_, err := docvault.NewDocumentLibraryClient(h.svcCtx.DocvaultRpcConn).UpdateDistributeStatusByWorkflowId(ctx, &docvault.UpdateDistributeStatusReq{
		WorkflowId: msg.WorkflowID,
		Status:     1,
	})
	if err != nil {
		logc.Errorf(ctx, "处理审批失败: %v", err)
	}
}

// handlePassed 处理审批通过事件
// 功能: 保存审批信息并发送消息中心通知
// 参数 ctx: 上下文 | msg: 审批事件消息
// 返回值: 无
// 错误类型: 无
func (h *DistributeApprovalConsumer) handlePassed(ctx context.Context, msg WorkflowEventMessage) {
	// 解析表单内容
	distributeApplication, err := h.parseFormContent(msg.FormContent)
	if err != nil {
		logc.Errorf(ctx, "解析表单内容失败: %v", err)
		return
	}
	// 获取作废文档id
	deprecatedDocuments, err := h.getDeprecatedDocuments(ctx, distributeApplication.Data)
	if err != nil {
		return
	}

	// 获取审批信息
	approvalInfo, err := GetApproval(ctx, h.svcCtx, msg)
	if err != nil {
		return
	}

	// 通过后，保存审批信息
	if err := h.saveApprovalInfo(ctx, msg.WorkflowID, approvalInfo, deprecatedDocuments); err != nil {
		logc.Errorf(ctx, "保存审批信息失败: %v", err)
		return
	}

	// 向接收人发送通知
	if err := h.processFormAndSendNotifications(ctx, msg.WorkflowID, distributeApplication); err != nil {
		logc.Errorf(ctx, "发送通知失败: %v", err)
		return
	}
}

// getDeprecatedDocuments 获取作废文档
func (h *DistributeApprovalConsumer) getDeprecatedDocuments(ctx context.Context, distributeApplicationData DistributeApplicationData) ([]*docvault.DeprecatedDocument, error) {
	// 搜集文档id，使用文档id查询是否已作废
	var docIDs []string
	for _, v := range distributeApplicationData.DistributeList {
		docIDs = append(docIDs, v.FileId)
	}
	// 查询哪些是已作废文档
	externalDocs, err := mapper.NewExternalDocumentLibraryClient(h.svcCtx.DocvaultDB).GetExternalDeprecatedDocumentIDs(ctx, docIDs)
	if err != nil {
		logc.Errorf(ctx, "查询文档是否作废失败: %v", err)
		return nil, err
	}
	internalDocs, err := mapper.NewInternalDocumentLibraryClient(h.svcCtx.DocvaultDB).GetInternalDeprecatedDocumentIds(ctx, docIDs)
	if err != nil {
		logc.Errorf(ctx, "查询文档是否作废失败: %v", err)
		return nil, err
	}
	// 如果文档已作废，则添加到deprecatedDocuments中
	deprecatedDocuments := make([]*docvault.DeprecatedDocument, 0)
	for _, v := range externalDocs {
		deprecatedDocuments = append(deprecatedDocuments, &docvault.DeprecatedDocument{
			DocumentId: v.ID,
			VersionNo:  v.Version,
		})
	}
	for _, v := range internalDocs {
		deprecatedDocuments = append(deprecatedDocuments, &docvault.DeprecatedDocument{
			DocumentId: v.ID,
			VersionNo:  v.Version,
		})
	}
	return deprecatedDocuments, nil
}

// saveApprovalInfo 保存审批信息
// 功能: 将审批信息保存到数据库
// 参数 ctx: 上下文 | workflowID: 工作流ID | approvalInfo: 审批信息
// 返回值: error 保存结果
// 错误类型: 数据库操作错误
func (h *DistributeApprovalConsumer) saveApprovalInfo(ctx context.Context, workflowID string, approvalInfo *docvault.ApprovalInfo, deprecatedDocuments []*docvault.DeprecatedDocument) error {
	_, err := docvault.NewDocumentLibraryClient(h.svcCtx.DocvaultRpcConn).SaveDistributeApproval(ctx, &docvault.DistributeApprovalReq{
		WorkflowId:          workflowID,
		Status:              3,
		ApprovalInfo:        approvalInfo,
		DeprecatedDocuments: deprecatedDocuments,
	})
	return err
}

// processFormAndSendNotifications 处理表单信息并发送通知
// 功能: 解析表单内容并发送消息中心通知
// 参数 ctx: 上下文 | msg: 审批事件消息
// 返回值: error 处理结果
// 错误类型: 表单解析错误、数据库查询错误
func (h *DistributeApprovalConsumer) processFormAndSendNotifications(ctx context.Context, workflowID string, distributeApplication *DistributeApplication) error {
	// 构建文档消息列表
	docMessages := h.buildDocMessages(distributeApplication.Data)

	// 获取发放记录ID
	distributeID, err := h.getDistributeID(ctx, workflowID)
	if err != nil {
		return err
	}

	// 发送消息中心通知
	return h.sendMessageCenterNotifications(ctx, distributeID, docMessages)
}

// parseFormContent 解析表单内容
// 功能: 将JSON格式的表单内容解析为结构体
// 参数 formContent: JSON格式的表单内容字符串
// 返回值: *DistributeApplication 解析后的表单数据 | error 解析结果
// 错误类型: JSON解析错误
func (h *DistributeApprovalConsumer) parseFormContent(formContent string) (*DistributeApplication, error) {
	var distributeApplication DistributeApplication
	if err := json.Unmarshal([]byte(formContent), &distributeApplication); err != nil {
		return nil, fmt.Errorf("解析表单内容失败: %v", err)
	}
	return &distributeApplication, nil
}

// buildDocMessages 构建文档消息列表
// 功能: 根据表单数据构建文档消息列表
// 参数 data: 表单数据
// 返回值: []DocMessage 文档消息列表
// 错误类型: 无
func (h *DistributeApprovalConsumer) buildDocMessages(data DistributeApplicationData) []DocMessage {
	var docMessages []DocMessage

	for _, distribute := range data.DistributeList {
		for _, permission := range distribute.Permissions {
			for _, received := range permission.ReceivedBy {
				docMessages = append(docMessages, DocMessage{
					UserID:         received.UserId,
					FileName:       distribute.FileName,
					Number:         distribute.Number,
					Version:        distribute.Version,
					FilePermission: getFilePermission(permission.FilePermission),
				})
			}
		}
	}

	return docMessages
}

// getDistributeID 获取发放记录ID
// 功能: 根据工作流ID查询发放记录ID
// 参数 ctx: 上下文 | workflowID: 工作流ID
// 返回值: string 发放记录ID | error 查询结果
// 错误类型: 数据库查询错误
func (h *DistributeApprovalConsumer) getDistributeID(ctx context.Context, workflowID string) (string, error) {
	distribute, err := mapper.NewDistributeRecordClient(h.svcCtx.DocvaultDB).FindByWorkflowID(ctx, workflowID)
	if err != nil {
		return "", fmt.Errorf("获取发放记录失败: %v", err)
	}
	return distribute.ID, nil
}

// sendMessageCenterNotifications 发送消息中心通知
// 功能: 批量发送消息中心通知给所有接收人
// 参数 ctx: 上下文 | distributeID: 发放记录ID | docMessages: 文档消息列表
// 返回值: error 发送结果
// 错误类型: 无（单个失败不影响整体）
func (h *DistributeApprovalConsumer) sendMessageCenterNotifications(ctx context.Context, distributeID string, docMessages []DocMessage) error {
	for _, docMessage := range docMessages {
		if err := h.sendSingleNotification(ctx, distributeID, docMessage); err != nil {
			logc.Errorf(ctx, "发送通知失败，用户ID: %s, 错误: %v", docMessage.UserID, err)
			// 继续发送其他用户的通知，不因为单个失败而中断
		}
	}

	return nil
}

// sendSingleNotification 发送单个通知
// 功能: 向单个用户发送消息中心通知
// 参数 ctx: 上下文 | userLoginInfo: 用户登录信息 | distributeID: 发放记录ID | docMessage: 文档消息
// 返回值: error 发送结果
// 错误类型: Kafka发送错误
func (h *DistributeApprovalConsumer) sendSingleNotification(ctx context.Context, distributeID string, docMessage DocMessage) error {
	// 构造消息
	content := h.buildNotificationContent(docMessage)
	businessType := "document_library_distribute" // 业务类型

	// 发送消息到Kafka
	return h.svcCtx.MessageCenterNoticeProducer.SendUrgentMessage(ctx, kqs.Message{
		BusinessID:   distributeID,
		BusinessType: businessType,
		Content:      content,
		RecipientID:  docMessage.UserID,
	})
}

// buildNotificationContent 构建通知内容
// 功能: 根据文档消息构建用户友好的通知内容
// 参数 docMessage: 文档消息
// 返回值: string 格式化的通知内容
// 错误类型: 无
func (h *DistributeApprovalConsumer) buildNotificationContent(docMessage DocMessage) string {
	return fmt.Sprintf("您已收到文件\"%s %s %s\"%s权限，请签收！",
		docMessage.FileName,
		docMessage.Number,
		docMessage.Version,
		docMessage.FilePermission,
	)
}

// getFilePermission 获取文件权限描述
// 功能: 将数字权限码转换为可读的权限描述
// 参数 filePermission: 文件权限数字码
// 返回值: string 权限描述文本
// 错误类型: 无
func getFilePermission(filePermission int) string {
	switch filePermission {
	case 1:
		return "查阅"
	case 2:
		return "查阅/下载"
	case 3:
		return "一次下载"
	default:
		return "未知权限"
	}
}

type DocMessage struct {
	UserID         string // 用户ID
	FileName       string // 文件名
	Number         string // 文件编号
	Version        string // 文件版本
	FilePermission string // 文件权限
}

// GetApproval 获取审批信息
// 功能: 根据工作流ID获取审批流程的审核人和审批人信息
// 参数 ctx: 上下文 | svcCtx: 服务上下文 | msg: 工作流事件消息
// 返回值: *docvault.ApprovalInfo 审批信息 | error 获取结果
// 错误类型: 工作流查询错误
func GetApproval(ctx context.Context, svcCtx *svc.ServiceContext, msg WorkflowEventMessage) (*docvault.ApprovalInfo, error) {
	// 构造用户洗信息
	userLoginInfo := utils.UserLoginInfo{
		UserId:         msg.SponsorID,
		TenantId:       msg.TenantID,
		OrganizationId: msg.OrganizationID,
		DeviceKind:     1,
		IsVirtualUser:  false,
	}
	ctx = userLoginInfo.SetContext(ctx)

	approveWorkflow, err := svcCtx.PhoenixClient.GetWorkflow(ctx, msg.WorkflowID)
	if err != nil {
		logc.Errorf(ctx, "获取审批流程信息失败: %v", err)
		return nil, err
	}
	approvalInfo := &docvault.ApprovalInfo{}
	if len(approveWorkflow.Nodes) == 0 {
		return approvalInfo, nil
	}
	// 如果节点长度为1，说明审核人和审批人是一样的
	if len(approveWorkflow.Nodes) == 1 {
		for _, approver := range approveWorkflow.Nodes[0].Approvers {
			approvalInfo.Auditors = append(approvalInfo.Auditors, &docvault.ApprovalInfoItem{
				UserId:     approver.ApproverID,
				PassedDate: approver.UpdatedAt,
			})
			approvalInfo.Approvers = append(approvalInfo.Approvers, &docvault.ApprovalInfoItem{
				UserId:     approver.ApproverID,
				PassedDate: approver.UpdatedAt,
			})
		}
		return approvalInfo, nil
	}
	// 节点长度为2，则第一个是审核人，第二个是审批人
	for _, approver := range approveWorkflow.Nodes[0].Approvers {
		approvalInfo.Auditors = append(approvalInfo.Auditors, &docvault.ApprovalInfoItem{
			UserId:     approver.ApproverID,
			PassedDate: approver.UpdatedAt,
		})
	}
	for _, approver := range approveWorkflow.Nodes[1].Approvers {
		approvalInfo.Approvers = append(approvalInfo.Approvers, &docvault.ApprovalInfoItem{
			UserId:     approver.ApproverID,
			PassedDate: approver.UpdatedAt,
		})
	}
	return approvalInfo, nil
}

type DistributeApplication struct {
	Data DistributeApplicationData `json:"data"`
}

type DistributeApplicationData struct {
	ID                 string `json:"ID"`
	Applicant          string `json:"applicant"`
	ApplyDate          int64  `json:"applyDate"`
	DistributeType     int    `json:"distributeType"`
	FileType           int    `json:"fileType"`
	TypeDictNodeId     string `json:"typeDictNodeId"`
	Reason             string `json:"reason"`
	OtherReason        string `json:"otherReason"`
	WishDistributeDate int64  `json:"wishDistributeDate"`
	DistributeList     []struct {
		FileId      string `json:"fileId"`
		FileName    string `json:"fileName"`
		Number      string `json:"number"`
		Version     string `json:"version"`
		Permissions []struct {
			FileForm       int    `json:"fileForm"`
			FilePermission int    `json:"filePermission"`
			Recipient      string `json:"recipient"`
			ReceivedBy     []struct {
				UserId   string `json:"userId"`
				UserName string `json:"userName"`
			} `json:"receivedBy"`
		} `json:"permissions"`
	} `json:"distributeList"`
	Category string `json:"category"`
}

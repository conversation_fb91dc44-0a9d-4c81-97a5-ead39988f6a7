package doclib_exporter

import (
	"context"
	"encoding/json"
	"nebula/internal/infrastructure/adapter/addons"
	"nebula/internal/infrastructure/adapter/grpc/pb/docvault"
	"nebula/internal/svc"
	"nebula/internal/types"
	"nebula/internal/utils"
	"strconv"
	"strings"
	"time"
)

type ExternalDocumentExporter struct {
	svcCtx *svc.ServiceContext
	ExporterAbility
}

func NewExternalDocumentExporter(svcCtx *svc.ServiceContext) ExporterAbility {
	exporter := &ExternalDocumentExporter{
		svcCtx:          svcCtx,
		ExporterAbility: NewExporterAbility(svcCtx),
	}
	return exporter
}

func (e *ExternalDocumentExporter) GetExportModelInfo(ctx context.Context) (fileName string, moduleName string, err error) {
	nickname := addons.NewQuickNameTranslatorImpl(e.svcCtx.RedisAddons).TranslateUserNickname(ctx, utils.GetContextUserID(ctx))
	return "外部文件库-" + nickname + "-" + time.Now().Format("20060102150405") + ".xlsx", "文件管理-外部文件库", nil
}

func (e *ExternalDocumentExporter) GetData(ctx context.Context, req any) ([][]string, error) {
	jsonReq, err := json.Marshal(req)
	if err != nil {
		return nil, err
	}
	var r types.GetExternalDocumentsReq
	err = json.Unmarshal(jsonReq, &r)
	if err != nil {
		return nil, err
	}
	beAttachedFile := ""
	if r.BeAttachedFile == 1 {
		beAttachedFile = "1"
	} else if r.BeAttachedFile == 2 {
		beAttachedFile = "2"
	}
	orgID := utils.GetContextOrganizationID(ctx)
	page, err := docvault.NewExternalDocumentLibraryClient(e.svcCtx.DocvaultRpcConn).Page(ctx, &docvault.ExternalDocumentPageReq{
		PageInfo: &docvault.PageInfo{
			NoPage: true,
		},
		Number:                         r.Number,
		Name:                           r.Name,
		OriginalNumber:                 r.OriginalNumber,
		OriginalDocNumber:              r.OriginalDocNumber,
		PublishDocNumber:               r.PublishDocNumber,
		PublishDepartment:              r.PublishDepartment,
		TypeDictionaryNodeIds:          r.TypeDictionaryNodeIds,
		AuthenticationDictionaryNodeId: r.AuthenticationDictionaryNodeId,
		BeAttachedFile:                 beAttachedFile,
		Status:                         int32(r.Status),
		OrgType:                        int32(r.OrgType),
		OrgId:                          orgID,
	})
	if err != nil {
		return nil, err
	}
	if len(page.Data) == 0 {
		return [][]string{}, nil
	}

	data := make([][]string, len(page.Data))
	for i, v := range page.Data {
		approvers := e.formatApprovalUsers(ctx, v.ApprovalInfo.Approvers)
		auditors := e.formatApprovalUsers(ctx, v.ApprovalInfo.Auditors)

		data[i] = []string{
			strconv.Itoa(i + 1),
			getStatus(v.Status),
			v.Number,
			v.Version,
			v.OriginalNumber,
			v.OriginalVersion,
			v.Name,
			v.EnglishName,
			v.DocType,
			v.OriginalDocNumber,
			v.PublishDocNumber,
			v.PublishDepartment,
			v.ReplacementDocName,
			v.ReplacementDocVersion,
			v.Remark,
			strings.Join(auditors, "/n"),
			strings.Join(approvers, "/n"),
			time.UnixMilli(v.PublishDate).Format(time.DateOnly),
			time.UnixMilli(v.EffectiveDate).Format(time.DateOnly),
			v.Authentication,
		}
	}
	return data, nil
}

func getStatus(status int32) string {
	switch status {
	case 1:
		return "即将作废"
	case 2:
		return "即将实施"
	case 3:
		return "有效"
	case 4:
		return "拟修订"
	}
	return ""
}

// formatApprovalUsers 格式化审批用户信息
// 功能：将审批用户列表转换为格式化的字符串数组
// 参数：
//   - ctx: 上下文
//   - users: 审批用户列表
//
// 返回值：格式化后的用户信息数组（用户名 + 审批日期）
func (e *ExternalDocumentExporter) formatApprovalUsers(ctx context.Context, users []*docvault.ApprovalInfoItem) []string {
	if len(users) == 0 {
		return []string{}
	}

	result := make([]string, 0, len(users))
	for _, user := range users {
		userName := e.svcCtx.QuickNameTranslator.TranslateUserNickname(ctx, user.UserId)
		approvalDate := time.UnixMilli(user.PassedDate).Format(time.DateOnly)
		result = append(result, userName+" "+approvalDate)
	}

	return result
}

func (e *ExternalDocumentExporter) GetTpl(ctx context.Context) (tplPath string, err error) {
	return "data/template/external_document_export.xlsx", nil
}

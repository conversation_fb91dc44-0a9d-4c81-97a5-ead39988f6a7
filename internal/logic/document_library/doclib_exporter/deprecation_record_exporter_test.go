package doclib_exporter

import (
	"context"
	"testing"

	"nebula/internal/types"

	. "github.com/smartystreets/goconvey/convey"
)

// TestDeprecationRecordExporter_GetExportModelInfo 测试获取导出模块信息
func TestDeprecationRecordExporter_GetExportModelInfo(t *testing.T) {
	Convey("测试获取作废记录导出模块信息", t, func() {
		// 跳过这个测试，因为它需要完整的服务上下文
		// 在实际使用中，这个方法会通过 NewDeprecationRecordExporter 正确初始化
		t.Skip("需要完整的服务上下文，跳过此测试")
	})
}

// TestDeprecationRecordExporter_GetDocumentModuleName 测试获取文档模块名称
func TestDeprecationRecordExporter_GetDocumentModuleName(t *testing.T) {
	Convey("测试获取文档模块名称", t, func() {
		exporter := &DeprecationRecordExporter{}

		Convey("书籍库", func() {
			result := exporter.getDocumentModuleName(1)
			So(result, ShouldEqual, "书籍库")
		})

		Convey("内部文档", func() {
			result := exporter.getDocumentModuleName(2)
			So(result, ShouldEqual, "内部文档")
		})

		Convey("外部文档", func() {
			result := exporter.getDocumentModuleName(3)
			So(result, ShouldEqual, "外部文档")
		})

		Convey("未知类型", func() {
			result := exporter.getDocumentModuleName(999)
			So(result, ShouldEqual, "未知")
		})
	})
}

// TestDeprecationRecordExporter_GetStatusName 测试获取状态名称
func TestDeprecationRecordExporter_GetStatusName(t *testing.T) {
	Convey("测试获取状态名称", t, func() {
		exporter := &DeprecationRecordExporter{}

		Convey("待提交", func() {
			result := exporter.getStatusName(1)
			So(result, ShouldEqual, "待提交")
		})

		Convey("审批中", func() {
			result := exporter.getStatusName(2)
			So(result, ShouldEqual, "审批中")
		})

		Convey("已审批", func() {
			result := exporter.getStatusName(3)
			So(result, ShouldEqual, "已审批")
		})

		Convey("已驳回", func() {
			result := exporter.getStatusName(4)
			So(result, ShouldEqual, "已驳回")
		})

		Convey("未知状态", func() {
			result := exporter.getStatusName(999)
			So(result, ShouldEqual, "未知")
		})
	})
}

// TestDeprecationRecordExporter_FormatDate 测试日期格式化
func TestDeprecationRecordExporter_FormatDate(t *testing.T) {
	Convey("测试日期格式化", t, func() {
		exporter := &DeprecationRecordExporter{}

		Convey("正常时间戳", func() {
			// 2023-01-01 00:00:00 的时间戳（毫秒）
			timestamp := int64(1672531200000)
			result := exporter.formatDate(timestamp)
			So(result, ShouldEqual, "2023-01-01")
		})

		Convey("零时间戳", func() {
			result := exporter.formatDate(0)
			So(result, ShouldEqual, "")
		})
	})
}

// TestDeprecationRecordExporter_GetApproverName 测试获取批准人姓名
func TestDeprecationRecordExporter_GetApproverName(t *testing.T) {
	Convey("测试获取批准人姓名", t, func() {
		exporter := &DeprecationRecordExporter{}

		Convey("有批准人", func() {
			approvalInfo := types.ApprovalInfo{
				Approvers: []types.Approval{
					{UserNickname: "王五"},
					{UserNickname: "赵六"},
				},
			}
			result := exporter.getApproverName(approvalInfo)
			So(result, ShouldEqual, "王五")
		})

		Convey("无批准人", func() {
			approvalInfo := types.ApprovalInfo{
				Approvers: []types.Approval{},
			}
			result := exporter.getApproverName(approvalInfo)
			So(result, ShouldEqual, "")
		})
	})
}

// TestDeprecationRecordExporter_ConvertToExcelRow 测试转换为Excel行数据
func TestDeprecationRecordExporter_ConvertToExcelRow(t *testing.T) {
	Convey("测试转换为Excel行数据", t, func() {
		exporter := &DeprecationRecordExporter{}

		record := types.DeprecateApplication{
			DocumentModuleType:     2,
			DocumentCategoryName:   "内部文件",
			DeprecateDocumentCount: 5,
			PlannedDeprecateDate:   1672531200000, // 2023-01-01
			Applicant:              "张三",
			ApplyDate:              1672531200000, // 2023-01-01
			ApprovalInfo: types.ApprovalInfo{
				Auditors:  []types.Approval{{UserNickname: "李四"}},
				Approvers: []types.Approval{{UserNickname: "王五"}},
			},
			Status: 3,
		}

		result := exporter.convertToExcelRow(1, record)

		So(len(result), ShouldEqual, 10)
		So(result[0], ShouldEqual, "1")          // 序号
		So(result[1], ShouldEqual, "内部文档")       // 文件编号（模块类型）
		So(result[2], ShouldEqual, "内部文件")       // 文件类别
		So(result[3], ShouldEqual, "5")          // 作废文件数
		So(result[4], ShouldEqual, "2023-01-01") // 拟定作废日期
		So(result[5], ShouldEqual, "张三")         // 申请人
		So(result[6], ShouldEqual, "2023-01-01") // 申请日期
		So(result[7], ShouldEqual, "李四")         // 审核人
		So(result[8], ShouldEqual, "王五")         // 批准人
		So(result[9], ShouldEqual, "已审批")        // 状态
	})
}

// TestDeprecationRecordExporter_GetTpl 测试获取模板路径
func TestDeprecationRecordExporter_GetTpl(t *testing.T) {
	Convey("测试获取模板路径", t, func() {
		exporter := &DeprecationRecordExporter{}

		tplPath, err := exporter.GetTpl(context.Background())

		So(err, ShouldBeNil)
		So(tplPath, ShouldEqual, "data/template/deprecated_export.xlsx")
	})
}

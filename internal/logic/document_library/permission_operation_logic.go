package document_library

import (
	"context"
	"errors"
	"fmt"
	"time"

	"nebula/internal/infrastructure/adapter/clientx/entity"
	"nebula/internal/infrastructure/adapter/grpc/pb/docvault"
	"nebula/internal/infrastructure/adapter/kqs"
	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/svc"
	"nebula/internal/types"
	"nebula/internal/utils"

	"github.com/zeromicro/go-zero/core/logx"
)

// DistributeInfo 发放信息缓存结构

type PermissionOperationLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewPermissionOperationLogic(ctx context.Context, svcCtx *svc.ServiceContext) *PermissionOperationLogic {
	return &PermissionOperationLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// PermissionOperation 权限操作接口
// 功能: 根据用户权限执行相应的文档操作，包括查阅和下载
// 业务规则:
//  1. 验证用户是否有该文档的指定权限
//  2. 如果有查阅权限，则生成水印文件并返回预览信息
//  3. 如果包含下载权限，则生成水印文件并推送到文件导出队列
//
// 参数:
//   - req: 权限操作请求，包含文档ID、文件形式、文件权限、清单ID
//
// 返回值:
//   - resp: 权限操作响应，包含文件ID和预览URL（仅查阅权限时返回）
//   - err: 错误信息
func (l *PermissionOperationLogic) PermissionOperation(req *types.PermissionOperationReq) (resp *types.PermissionOperationResp, err error) {
	// 参数验证
	if err := l.validateRequest(req); err != nil {
		l.Logger.Errorf("参数验证失败: %v", err)
		return nil, err
	}

	// 校验是否存在审批中
	if err := l.checkIsApproving(req.InventoryID); err != nil {
		return nil, err
	}

	// 校验是否已签收
	if err := l.checkIsSign(req); err != nil {
		return nil, err
	}

	// 获取当前用户ID
	currentUserID := utils.GetContextUserID(l.ctx)

	// 获取发放信息（一次性查询，避免重复）
	distributeInfo, err := l.getDistributeInfo(req.InventoryID)
	if err != nil {
		l.Logger.Errorf("获取发放信息失败: %v", err)
		return nil, fmt.Errorf("获取发放信息失败: %w", err)
	}

	if distributeInfo.ActualFileID == "" {
		return &types.PermissionOperationResp{}, nil
	}

	if err = l.check(distributeInfo, req, currentUserID); err != nil {
		return nil, err
	}

	// 根据操作类型执行不同操作
	switch req.OperationType {
	case 1: // 查阅操作 - 直接返回实际文件ID，不加水印
		resp, err = l.handleViewOperationFromInfo(distributeInfo)
	case 2: // 下载操作 - 需要生成水印文件
		resp, err = l.handleDownloadOperation(distributeInfo, currentUserID, req)
	default:
		return nil, fmt.Errorf("不支持的操作类型: %d", req.OperationType)
	}
	if err != nil {
		return nil, err
	}
	// 更新权限使用状态
	_, err = docvault.NewDocumentLibraryClient(l.svcCtx.DocvaultRpcConn).UpdatePermissionUsedStatus(l.ctx, &docvault.UpdatePermissionUsedStatusReq{
		DistributeRecordFileId: distributeInfo.DistributeFile.ID,
		FileForm:               int32(req.FileForm),
		FilePermission:         int32(req.FilePermission),
		UserId:                 currentUserID,
		IsUsed:                 true,
	})
	if err != nil {
		return nil, fmt.Errorf("更新用户处置状态失败: %w", err)
	}

	return resp, nil
}

// checkIsApproving 校验是否存在审批中
// 功能: 根据清单ID查询发放记录状态，判断是否处于审批中
//
// 参数:
//   - inventoryID: 清单ID
//
// 返回值:
//   - err: 错误信息，当存在审批中时，返回提示信息
func (l *PermissionOperationLogic) checkIsApproving(inventoryID string) error {
	// 查询清单信息
	distributeRecordFile, err := mapper.NewDistributeRecordFileClient(l.svcCtx.DocvaultDB).FindByID(l.ctx, inventoryID)
	if err != nil {
		return fmt.Errorf("查询发放记录文件的权限失败: %w", err)
	}
	// 查询发放记录
	distributeRecord, err := mapper.NewDistributeRecordClient(l.svcCtx.DocvaultDB).FindByID(l.ctx, distributeRecordFile.RecordID)
	if err != nil {
		return fmt.Errorf("查询发放记录失败: %w", err)
	}

	// 检查发放记录状态
	if distributeRecord.Status == 2 {
		return errors.New("当前已有申请流程，请在流程完成后重试。")
	}
	return nil
}

// checkIsSign 校验是否已签收
// 功能: 查询该权限是否已签收
//
// 参数:
//   - req: 权限操作请求，包含文档ID、文件形式、文件权限、清单ID
//
// 返回值:
//   - err: 错误信息，如果未签收返回错误消息
func (l *PermissionOperationLogic) checkIsSign(req *types.PermissionOperationReq) error {
	distributeRecordFileClient := mapper.NewDistributeRecordPermissionClient(l.svcCtx.DocvaultDB)
	distributeRecordFile, err := distributeRecordFileClient.FindByFileRecordIDWithFileFormAndPermission(l.ctx, req.InventoryID, int(req.FileForm), int(req.FilePermission))
	if err != nil {
		return fmt.Errorf("查询发放记录文件的权限失败: %w", err)
	}
	if distributeRecordFile.SignForStatus == 1 {
		return errors.New("请前往“消息提醒”模块签收文件")
	}
	return nil
}

// check 检查发放记录状态和用户权限
func (l *PermissionOperationLogic) check(distributeInfo *distributeInfo, req *types.PermissionOperationReq, currentUserID string) error {
	canOperate := l.checkDistributeApprovalAndDateFromInfo(distributeInfo.DistributeRecord)
	if !canOperate {
		l.Logger.Errorf("发放记录未审批完成或期望发送日期未到，清单ID: %s", req.InventoryID)
		return fmt.Errorf("发放记录未审批完成或期望发送日期未到")
	}

	// 验证用户权限
	hasPermission, err := l.checkUserPermission(currentUserID, req)
	if err != nil {
		l.Logger.Errorf("验证用户权限失败: %v", err)
		return fmt.Errorf("验证用户权限失败: %w", err)
	}

	if !hasPermission {
		l.Logger.Errorf("用户 %s 没有文档 %s 的权限", currentUserID, req.DocumentID)
		return fmt.Errorf("您没有该文档的访问权限")
	}

	//检查版本号
	if distributeInfo.DistributeVersion != distributeInfo.DocumentVersion {
		return fmt.Errorf("文档已更新，请重新进行发放申请")
	}

	return nil
}

// getDistributeInfo 获取发放信息（带缓存）
// 功能: 获取发放记录信息，包括文档名称和实际文件ID，避免重复查询
// 参数:
//   - inventoryID: 清单ID
//
// 返回值:
//   - *DistributeInfo: 发放信息
//   - error: 错误信息
func (l *PermissionOperationLogic) getDistributeInfo(inventoryID string) (*distributeInfo, error) {

	// 创建数据库客户端
	distributeFileClient := mapper.NewDistributeRecordFileClient(l.svcCtx.DocvaultDB)
	distributeRecordClient := mapper.NewDistributeRecordClient(l.svcCtx.DocvaultDB)

	// 根据清单ID查询发放记录文件
	distributeFile, err := distributeFileClient.FindByID(l.ctx, inventoryID)
	if err != nil {
		return nil, fmt.Errorf("查询发放记录文件失败: %w", err)
	}

	// 根据RecordID查询发放记录
	distributeRecord, err := distributeRecordClient.FindByID(l.ctx, distributeFile.RecordID)
	if err != nil {
		return nil, fmt.Errorf("查询发放记录失败: %w", err)
	}

	// 获取实际文件ID和文档名称
	actualFileID, documentName, documentVersion, err := l.getDocumentInfoByType(distributeRecord.FileType, distributeFile.FileID)
	if err != nil {
		return nil, fmt.Errorf("获取文档信息失败: %w", err)
	}

	return &distributeInfo{
		DistributeFile:    distributeFile,
		DistributeRecord:  distributeRecord,
		DocumentName:      documentName,
		ActualFileID:      actualFileID,
		DistributeVersion: distributeFile.Version,
		DocumentVersion:   documentVersion,
	}, nil
}

// getDocumentInfoByType 根据文件类型获取文档信息
// 功能: 根据文件类型查询对应的文档库，获取文件ID和文档名称
// 参数:
//   - fileType: 文件类型（1=内部文件，2=外部文件）
//   - documentID: 文档ID
//
// 返回值:
//   - string: 实际文件ID
//   - string: 文档名称
//   - string: 文档版本
//   - error: 错误信息
func (l *PermissionOperationLogic) getDocumentInfoByType(fileType int32, documentID string) (string, string, string, error) {
	switch fileType {
	case 1: // 内部文件
		internalDocClient := mapper.NewInternalDocumentLibraryClient(l.svcCtx.DocvaultDB)
		internalDoc, err := internalDocClient.GetByID(l.ctx, documentID)
		if err != nil {
			return "", "", "", fmt.Errorf("查询内部文档库失败: %w", err)
		}

		return internalDoc.FileID, internalDoc.Name, internalDoc.Version, nil

	case 2: // 外部文件
		externalDocClient := mapper.NewExternalDocumentLibraryClient(l.svcCtx.DocvaultDB)
		externalDoc, err := externalDocClient.GetByID(l.ctx, documentID)
		if err != nil {
			return "", "", "", fmt.Errorf("查询外部文档库失败: %w", err)
		}

		return externalDoc.FileID, externalDoc.Name, externalDoc.Version, nil

	default:
		return "", "", "", fmt.Errorf("不支持的文件类型: %d", fileType)
	}
}

// checkDistributeApprovalAndDateFromInfo 从发放记录信息检查审批状态和期望发送日期
// 功能: 检查发放记录是否已审批完成且期望发送日期已到
// 参数:
//   - distributeRecord: 发放记录
//
// 返回值:
//   - bool: 是否可以操作
func (l *PermissionOperationLogic) checkDistributeApprovalAndDateFromInfo(distributeRecord mapper.DistributeRecord) err {
	// 检查审批状态：必须是已审批(3)
	if distributeRecord.Status != 3 {
		l.Logger.Infof("发放记录未审批完成，当前状态: %d", distributeRecord.Status)
		return false
	}

	// 检查期望发送日期：必须在今天之前或等于今天
	// 日期应该是当天零点
	now := time.Now().Truncate(24 * time.Hour)
	today := time.Now().UnixMilli()
	if distributeRecord.WishDistributeDate > today {
		l.Logger.Infof("期望发送日期未到，期望日期: %d, 当前日期: %d",
			distributeRecord.WishDistributeDate, today)
		return false
	}

	l.Logger.Infof("发放记录检查通过，状态: %d, 期望日期: %d",
		distributeRecord.Status, distributeRecord.WishDistributeDate)
	return true
}

// handleViewOperationFromInfo 处理查阅操作（使用缓存信息）
// 功能: 对于查阅操作，直接使用缓存的实际文件ID，不需要生成水印
// 参数:
//   - distributeInfo: 发放信息
//
// 返回值:
//   - *types.PermissionOperationResp: 响应信息，包含实际文件ID
//   - error: 错误信息
func (l *PermissionOperationLogic) handleViewOperationFromInfo(distributeInfo *distributeInfo) (*types.PermissionOperationResp, error) {
	resp := &types.PermissionOperationResp{
		FileID: distributeInfo.ActualFileID,
	}

	l.Logger.Infof("查阅操作处理完成，返回实际文件ID: %s", resp.FileID)
	return resp, nil
}

// generateWatermarkFileFromInfo 生成水印文件（使用缓存信息）
// 功能: 为指定文档生成带有用户信息的水印文件，根据权限类型、文件类型和发放类型生成不同的水印内容
// 参数:
//   - documentID: 文档ID（实际文件ID）
//   - userID: 用户ID
//   - filePermission: 文件权限类型 (1=查阅, 2=查阅/下载, 3=一次下载)
//   - distributeInfo: 发放信息
//   - req: 权限操作请求（包含清单ID、文件形式、文件权限等信息）
//
// 返回值:
//   - entity.GenerateWatermarkedFileInfo: 水印文件信息
//   - error: 错误信息
func (l *PermissionOperationLogic) generateWatermarkFileFromInfo(documentID, userID string, filePermission int64, distributeInfo *distributeInfo, req *types.PermissionOperationReq) (entity.GenerateWatermarkedFileInfo, error) {
	// 获取用户昵称
	userNickname := l.svcCtx.QuickNameTranslator.TranslateUserNickname(l.ctx, userID)
	if userNickname == "" {
		userNickname = "未知用户"
	}

	// 根据权限类型生成不同的水印内容
	var watermarkText string
	switch filePermission {
	case 1: // 查阅权限 - 在线预览水印
		watermarkText = l.generatePreviewWatermark(userNickname)
	case 2, 3: // 查阅/下载权限 或 一次下载权限 - 需要根据文件类型和发放类型生成下载水印
		var err error
		watermarkText, err = l.generateDownloadWatermarkFromInfo(distributeInfo, req)
		if err != nil {
			return entity.GenerateWatermarkedFileInfo{}, fmt.Errorf("生成下载水印失败: %w", err)
		}
	default:
		return entity.GenerateWatermarkedFileInfo{}, fmt.Errorf("不支持的权限类型: %d", filePermission)
	}

	watermarkReq := entity.GenerateWatermarkedFileReq{
		ID:            documentID,
		WatermarkText: watermarkText,
		// 设置默认水印样式
		FontSize: 20,
		Opacity:  0.5,
		Rotation: 15,
		XOffset:  100,
		YOffset:  100,
	}

	// 调用Phoenix客户端生成水印文件
	watermarkInfo, err := l.svcCtx.PhoenixClient.GenerateWatermarkedFile(l.ctx, watermarkReq)
	if err != nil {
		return entity.GenerateWatermarkedFileInfo{}, fmt.Errorf("调用Phoenix生成水印文件失败: %w", err)
	}

	return watermarkInfo, nil
}

// generatePreviewWatermark 生成在线预览水印内容
// 功能: 生成在线预览时使用的水印文本
// 参数:
//   - userNickname: 用户昵称
//
// 返回值:
//   - string: 水印文本内容
func (l *PermissionOperationLogic) generatePreviewWatermark(userNickname string) string {
	previewDate := time.Now().Format("2006-01-02")
	return fmt.Sprintf("%s %s 本文件涉密 禁止拍照或截图", previewDate, userNickname)
}

// generateDownloadWatermarkFromInfo 生成下载水印内容（使用缓存信息）
// 功能: 根据文件类型和发放类型生成相应的下载水印文本
// 参数:
//   - distributeInfo: 发放信息
//   - req: 权限操作请求（包含清单ID、文件形式、文件权限等信息）
//
// 返回值:
//   - string: 水印文本内容
//   - error: 错误信息
func (l *PermissionOperationLogic) generateDownloadWatermarkFromInfo(distributeInfo *distributeInfo, req *types.PermissionOperationReq) (string, error) {
	// 根据文件类型和发放类型生成水印内容
	switch distributeInfo.DistributeRecord.FileType {
	case 1: // 内部文件
		return l.generateInternalFileDownloadWatermarkFromInfo(distributeInfo.DistributeRecord, req)
	case 2: // 外部文件
		return l.generateExternalFileDownloadWatermarkFromInfo(distributeInfo.DistributeRecord, req), nil
	default:
		return "", fmt.Errorf("不支持的文件类型: %d", distributeInfo.DistributeRecord.FileType)
	}
}

// generateInternalFileDownloadWatermarkFromInfo 生成内部文件下载水印内容（使用缓存信息）
// 功能: 根据发放类型生成内部文件的下载水印
// 参数:
//   - distributeRecord: 发放记录
//   - req: 权限操作请求（包含清单ID、文件形式、文件权限等信息）
//
// 返回值:
//   - string: 水印文本内容
//   - error: 错误信息
func (l *PermissionOperationLogic) generateInternalFileDownloadWatermarkFromInfo(distributeRecord mapper.DistributeRecord, req *types.PermissionOperationReq) (string, error) {
	switch distributeRecord.DistributeType {
	case 1: // 内部发放
		// 获取组织名称
		orgName := l.svcCtx.QuickNameTranslator.TranslateOrganizationName(l.ctx, distributeRecord.OrganizationID)
		return fmt.Sprintf("文件下载后单次生效，可能存在变更，实际文件以系统内为准\n%s 内部使用文件", orgName), nil
	case 2: // 外部发放
		// 获取审批日期和接收方信息
		approvalDate := l.getApprovalDateFromInfo(distributeRecord)
		recipient := l.getRecipientInfoFromInventory(req.InventoryID, req.FileForm, req.FilePermission)
		if recipient != "" {
			recipient = fmt.Sprintf("\n接收方：%s·严禁转发", recipient)
		}

		return fmt.Sprintf("文件下载后单次生效，可能存在变更，实际文件以系统内为准\n参考文件，变更不予通知/作废不予回收\n  发放日期：%s%s", approvalDate, recipient), nil
	default:
		// 默认使用普通内部文件下载水印
		return "文件下载后单次生效，可能存在变更，实际文件以系统内为准", nil
	}
}

// getApprovalDateFromInfo 获取审批通过日期（使用缓存信息）
// 功能: 从发放记录的审批信息中获取审批通过日期
// 参数:
//   - distributeRecord: 发放记录
//
// 返回值:
//   - string: 审批日期（格式：YYYY-MM-DD）
func (l *PermissionOperationLogic) getApprovalDateFromInfo(distributeRecord mapper.DistributeRecord) string {
	// 这里先返回默认值，实际应该解析JSON获取审批通过的日期
	if distributeRecord.UpdatedAt.IsZero() {
		return "XXXXXX"
	}
	return distributeRecord.UpdatedAt.Format("2006-01-02")
}

// handleViewDownloadPermissionFromInfo 处理查阅/下载权限（使用缓存信息）
// 功能: 对于查阅/下载权限，生成水印文件并推送到导出队列，同时返回预览信息
// 参数:
//   - watermarkInfo: 水印文件信息
//   - userID: 用户ID
//   - distributeInfo: 发放信息
//
// 返回值:
//   - *types.PermissionOperationResp: 响应信息，包含文件ID和预览URL
//   - error: 错误信息
func (l *PermissionOperationLogic) handleViewDownloadPermissionFromInfo(watermarkInfo entity.GenerateWatermarkedFileInfo, userID string, distributeInfo *distributeInfo) (*types.PermissionOperationResp, error) {
	// 推送到文件导出队列
	if err := l.pushToExportQueueFromInfo(watermarkInfo, userID, "查阅/下载", distributeInfo); err != nil {
		l.Logger.Errorf("推送到导出队列失败: %v", err)
		return nil, fmt.Errorf("推送到导出队列失败: %w", err)
	}

	// 同时返回预览信息供查阅使用
	resp := &types.PermissionOperationResp{}
	if watermarkInfo.ID != nil {
		resp.FileID = *watermarkInfo.ID
	}
	if watermarkInfo.PreviewURL != nil {
		resp.PreviewURL = *watermarkInfo.PreviewURL
	}

	l.Logger.Infof("查阅/下载权限处理完成，已推送到导出队列并返回预览信息: FileID=%s", resp.FileID)
	return resp, nil
}

// handleOneTimeDownloadPermissionFromInfo 处理一次下载权限（使用缓存信息）
// 功能: 对于一次下载权限，生成水印文件并推送到导出队列
// 参数:
//   - watermarkInfo: 水印文件信息
//   - userID: 用户ID
//   - distributeInfo: 发放信息
//
// 返回值:
//   - *types.PermissionOperationResp: 响应信息
//   - error: 错误信息
func (l *PermissionOperationLogic) handleOneTimeDownloadPermissionFromInfo(watermarkInfo entity.GenerateWatermarkedFileInfo, userID string, distributeInfo *distributeInfo, fileForm int64) (*types.PermissionOperationResp, error) {
	// 推送到文件导出队列
	if err := l.pushToExportQueueFromInfo(watermarkInfo, userID, "一次下载", distributeInfo); err != nil {
		l.Logger.Errorf("推送到导出队列失败: %v", err)
		return nil, fmt.Errorf("推送到导出队列失败: %w", err)
	}

	// 一次下载权限不返回预览信息，只返回成功状态
	resp := &types.PermissionOperationResp{}
	if watermarkInfo.ID != nil {
		resp.FileID = *watermarkInfo.ID
	}

	return resp, nil
}

// pushToExportQueueFromInfo 推送到文件导出队列（使用缓存信息）
// 功能: 将水印文件信息推送到Kafka导出队列，使用缓存的发放信息避免重复查询
// 参数:
//   - watermarkInfo: 水印文件信息
//   - userID: 用户ID
//   - operationType: 操作类型（查阅/下载、一次下载）
//   - distributeInfo: 发放信息
//
// 返回值:
//   - error: 错误信息
func (l *PermissionOperationLogic) pushToExportQueueFromInfo(watermarkInfo entity.GenerateWatermarkedFileInfo, userID, operationType string, distributeInfo *distributeInfo) error {
	if l.svcCtx.KafkaDataExportProducer == nil {
		return fmt.Errorf("Kafka导出生产者未初始化")
	}

	// 根据文件类型确定模块名称
	var moduleName string
	switch distributeInfo.DistributeRecord.FileType {
	case 1: // 内部文件
		moduleName = "文件管理-内部文件库"
	case 2: // 外部文件
		moduleName = "文件管理-外部文件库"
	default:
		moduleName = "文件管理-未知文件库"
	}

	exportInfo := kqs.DataExportModelInfo{
		TaskID:     l.svcCtx.IdGenerator.GenerateIDString(),
		FileName:   distributeInfo.DocumentName,
		ModuleName: moduleName,
		UserID:     userID,
		Status:     kqs.DataExportStatusProgress, // 第一步：推送导出中状态
	}

	// 第一步：推送导出中状态
	if err := l.svcCtx.KafkaDataExportProducer.SendMessage(l.ctx, exportInfo); err != nil {
		return fmt.Errorf("推送导出中状态失败: %w", err)
	}

	// 推送导出完成状态
	exportInfo.Status = kqs.DataExportStatusComplete
	if watermarkInfo.ID != nil {
		exportInfo.FileID = *watermarkInfo.ID
	}

	if err := l.svcCtx.KafkaDataExportProducer.SendMessage(l.ctx, exportInfo); err != nil {
		l.Logger.Errorf("推送导出完成状态失败: %v", err)
	} else {
		l.Logger.Infof("已推送导出完成状态到队列，TaskID: %s, FileID: %s", exportInfo.TaskID, exportInfo.FileID)
	}

	return nil
}

// validateRequest 验证请求参数
// 功能: 验证权限操作请求的必要参数
// 参数:
//   - req: 权限操作请求
//
// 返回值:
//   - error: 验证失败时返回错误信息
func (l *PermissionOperationLogic) validateRequest(req *types.PermissionOperationReq) error {
	if req.DocumentID == "" {
		return fmt.Errorf("文档ID不能为空")
	}
	if req.InventoryID == "" {
		return fmt.Errorf("清单ID不能为空")
	}
	if req.FileForm != 1 && req.FileForm != 2 {
		return fmt.Errorf("文件形式必须为1(电子文件)或2(纸质文件)")
	}
	if req.FilePermission < 1 || req.FilePermission > 3 {
		return fmt.Errorf("文件权限必须为1(查阅)、2(查阅/下载)或3(一次下载)")
	}
	return nil
}

// checkUserPermission 检查用户权限
// 功能: 验证当前用户是否有指定文档的权限
// 参数:
//   - userID: 用户ID
//   - req: 权限操作请求
//
// 返回值:
//   - bool: 是否有权限
//   - error: 错误信息
func (l *PermissionOperationLogic) checkUserPermission(userID string, req *types.PermissionOperationReq) (bool, error) {
	// 创建权限查询客户端
	permissionClient := mapper.NewDistributeRecordPermissionClient(l.svcCtx.DocvaultDB)

	// 转换为指针类型，用于数据库查询
	fileForm := int32(req.FileForm)
	filePermission := int32(req.FilePermission)

	// 查询用户对该文档的权限记录，直接在数据库层过滤 fileForm 和 filePermission
	permissions, err := permissionClient.FindByFileRecordIDsWithStatusAndPermission(l.ctx, []string{req.InventoryID}, userID, &fileForm, &filePermission)
	if err != nil {
		return false, fmt.Errorf("查询用户权限失败: %w", err)
	}

	var hasFlow bool
	// 检查是否有匹配的权限记录
	for _, permission := range permissions {
		if permission.DistributeRecordStatus == 1 {
			// 流程中
			hasFlow = true
			continue
		} else if permission.DistributeRecordStatus == 3 && permission.DisposeStatus < 3 {
			// 已审批 并且未回收
			return true, nil
		}
	}

	if hasFlow {
		return false, errors.New("当前已有申请流程，请在流程完成后重试")
	}

	return false, nil
}

// generateExternalFileDownloadWatermarkFromInfo 生成外部文件下载水印内容（使用发放记录信息）
// 功能: 根据发放类型生成外部文件的下载水印
// 参数:
//   - distributeRecord: 发放记录
//   - req: 权限操作请求（包含清单ID、文件形式、文件权限等信息）
//
// 返回值:
//   - string: 水印文本内容
func (l *PermissionOperationLogic) generateExternalFileDownloadWatermarkFromInfo(distributeRecord mapper.DistributeRecord, req *types.PermissionOperationReq) string {
	return "中一检测 内部收藏"
}

type distributeInfo struct {
	DistributeFile    mapper.DistributeRecordFile
	DistributeRecord  mapper.DistributeRecord
	DocumentName      string
	ActualFileID      string
	DocumentVersion   string
	DistributeVersion string
}

// handleDownloadOperation 处理下载操作
// 功能: 生成水印文件并根据权限类型执行相应的下载操作
// 参数:
//   - distributeInfo: 发放信息
//   - currentUserID: 当前用户ID
//   - req: 权限操作请求
//
// 返回值:
//   - *types.PermissionOperationResp: 操作响应
//   - error: 错误信息
func (l *PermissionOperationLogic) handleDownloadOperation(distributeInfo *distributeInfo, currentUserID string, req *types.PermissionOperationReq) (*types.PermissionOperationResp, error) {
	// 生成水印文件
	watermarkFileInfo, err := l.generateWatermarkFileFromInfo(distributeInfo.ActualFileID, currentUserID, req.FilePermission, distributeInfo, req)
	if err != nil {
		l.Logger.Errorf("生成水印文件失败: %v", err)
		return nil, fmt.Errorf("生成水印文件失败: %w", err)
	}

	// 根据权限类型执行不同的下载操作
	switch req.FilePermission {
	case 2: // 查阅/下载权限
		return l.handleViewDownloadPermissionFromInfo(watermarkFileInfo, currentUserID, distributeInfo)
	case 3: // 一次下载权限
		return l.handleOneTimeDownloadPermissionFromInfo(watermarkFileInfo, currentUserID, distributeInfo, req.FileForm)
	default:
		l.Logger.Errorf("下载操作不支持的权限类型: %d", req.FilePermission)
		return nil, fmt.Errorf("下载操作不支持的权限类型: %d", req.FilePermission)
	}
}

// getRecipientInfoFromInventory 根据清单ID和权限信息获取接收方信息
// 功能: 根据清单ID、文件形式和文件权限查询具体的权限记录，获取真实的接收方信息
// 参数:
//   - inventoryID: 清单ID（发放记录文件ID）
//   - fileForm: 文件形式（1=电子文件，2=纸质文件）
//   - filePermission: 文件权限（1=查阅，2=查阅/下载，3=一次下载）
//
// 返回值:
//   - string: 接收方信息（优先使用权限记录中的接收方，其次是用户名）
func (l *PermissionOperationLogic) getRecipientInfoFromInventory(inventoryID string, fileForm, filePermission int64) string {
	// 查询权限记录
	permissionClient := mapper.NewDistributeRecordPermissionClient(l.svcCtx.DocvaultDB)

	// 转换为指针类型，用于数据库查询
	fileFormPtr := int32(fileForm)
	filePermissionPtr := int32(filePermission)

	// 查询具体的权限记录
	permissions, err := permissionClient.FindByFileRecordIDsWithStatusAndPermission(
		l.ctx,
		[]string{inventoryID},
		"", // 不限制用户ID，获取所有权限记录
		&fileFormPtr,
		&filePermissionPtr,
	)
	if err != nil || len(permissions) == 0 {
		return ""
	}
	if len(permissions) > 0 {
		return permissions[0].Recipient
	}
	return ""
}

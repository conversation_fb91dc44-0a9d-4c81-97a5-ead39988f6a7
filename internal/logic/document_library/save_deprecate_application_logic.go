package document_library

import (
	"context"
	"fmt"

	"nebula/internal/infrastructure/adapter/grpc/pb/docvault"
	"nebula/internal/svc"
	"nebula/internal/types"
	"nebula/internal/utils"

	"github.com/zeromicro/go-zero/core/logx"
)

type SaveDeprecateApplicationLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewSaveDeprecateApplicationLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SaveDeprecateApplicationLogic {
	return &SaveDeprecateApplicationLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *SaveDeprecateApplicationLogic) SaveDeprecateApplication(req *types.SaveDeprecateApplicationReq) (resp *types.SaveDeprecateApplicationResp, err error) {
	// 1. 参数验证
	if err := l.validateRequest(req); err != nil {
		return nil, err
	}

	// 2. 获取用户和组织信息
	userID := utils.GetContextUserID(l.ctx)
	orgID := utils.GetContextOrganizationID(l.ctx)
	tenantID := utils.GetContextTenantID(l.ctx)

	if userID == "" || orgID == "" || tenantID == "" {
		return nil, fmt.Errorf("缺少用户或组织信息")
	}

	// 3. 判断是新增还是编辑
	var deprecationID string
	if req.ID == "" {
		// 新增作废申请
		deprecationID, err = l.createDeprecationRecord(req, userID)
	} else {
		// 编辑作废申请
		deprecationID, err = l.updateDeprecationRecord(req, userID)
	}

	if err != nil {
		l.Errorf("保存作废申请失败: %v", err)
		return nil, err
	}

	return &types.SaveDeprecateApplicationResp{
		ID: deprecationID,
	}, nil
}

// validateRequest 验证请求参数
func (l *SaveDeprecateApplicationLogic) validateRequest(req *types.SaveDeprecateApplicationReq) error {
	if req.PlannedDeprecateDate <= 0 {
		return fmt.Errorf("拟定作废日期不能为空")
	}

	if req.DocumentModuleType <= 0 {
		return fmt.Errorf("文档模块类型不能为空")
	}

	if req.DocumentCategoryID == "" {
		return fmt.Errorf("文档类别不能为空")
	}

	if req.DeprecateReason <= 0 {
		return fmt.Errorf("作废原因不能为空")
	}

	// 如果作废原因是"其他"（假设为9），则其他原因描述必填
	if req.DeprecateReason == 9 && req.OtherReason == "" {
		return fmt.Errorf("请输入其他原因")
	}

	if len(req.DeprecateList) == 0 {
		return fmt.Errorf("作废清单不能为空")
	}

	// 验证作废清单中的文档ID不能为空
	for i, doc := range req.DeprecateList {
		if doc.DocumentID == "" {
			return fmt.Errorf("第%d个文档的ID不能为空", i+1)
		}
	}

	return nil
}

// createDeprecationRecord 创建作废记录
func (l *SaveDeprecateApplicationLogic) createDeprecationRecord(req *types.SaveDeprecateApplicationReq, userID string) (string, error) {
	// 构建 gRPC 请求
	grpcReq := &docvault.AddDeprecationRecordReq{
		DeprecateAt:    req.PlannedDeprecateDate,
		Reason:         req.DeprecateReason,
		OtherReason:    req.OtherReason,
		UserId:         userID,
		ApprovalStatus: req.Status,
		Documents:      make([]*docvault.CreateDeprecationDocumentInfo, 0, len(req.DeprecateList)),
	}

	// 转换作废文档列表
	for _, doc := range req.DeprecateList {
		grpcReq.Documents = append(grpcReq.Documents, &docvault.CreateDeprecationDocumentInfo{
			DocumentId:     doc.DocumentID,
			DocumentStatus: 1, // 1表示即将作废
		})
	}

	// 调用 gRPC 服务
	resp, err := docvault.NewDocumentLibraryClient(l.svcCtx.DocvaultRpcConn).AddDeprecationRecord(l.ctx, grpcReq)
	if err != nil {
		return "", fmt.Errorf("调用创建作废记录服务失败: %v", err)
	}

	return resp.Id, nil
}

// updateDeprecationRecord 更新作废记录
func (l *SaveDeprecateApplicationLogic) updateDeprecationRecord(req *types.SaveDeprecateApplicationReq, userID string) (string, error) {
	// 构建 gRPC 请求
	grpcReq := &docvault.UpdateDeprecationRecordReq{
		Id:             req.ID,
		DeprecateAt:    req.PlannedDeprecateDate,
		Reason:         req.DeprecateReason,
		OtherReason:    req.OtherReason,
		ApprovalStatus: req.Status,
		Documents:      make([]*docvault.UpdateDeprecationDocumentInfo, 0, len(req.DeprecateList)),
		WorkflowId:     "", // 工作流ID暂时为空，后续可根据业务需要设置
	}

	// 转换作废文档列表
	for _, doc := range req.DeprecateList {
		grpcReq.Documents = append(grpcReq.Documents, &docvault.UpdateDeprecationDocumentInfo{
			DocumentId: doc.DocumentID,
		})
	}

	// 调用 gRPC 服务
	_, err := docvault.NewDocumentLibraryClient(l.svcCtx.DocvaultRpcConn).UpdateDeprecationRecord(l.ctx, grpcReq)
	if err != nil {
		return "", fmt.Errorf("调用更新作废记录服务失败: %v", err)
	}

	return req.ID, nil
}

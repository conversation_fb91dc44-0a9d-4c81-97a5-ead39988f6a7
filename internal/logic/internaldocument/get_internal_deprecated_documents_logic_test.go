package internaldocument

import (
	"context"
	"fmt"
	"testing"
	"time"

	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/svc"
	"nebula/internal/types"
	"nebula/internal/utils"

	. "github.com/smartystreets/goconvey/convey"
)

// TestData 测试数据结构
// 功能：存储测试过程中创建的数据，便于清理
type TestData struct {
	InternalDocuments []mapper.InternalDocumentLibrary // 内部文档列表
}

// createTestData 创建测试数据
// 功能：创建测试所需的内部作废文档数据
// 参数：svcCtx - 服务上下文
// 返回值：测试数据，错误信息
func createTestData(svcCtx *svc.ServiceContext) (*TestData, error) {
	ctx := context.Background()
	now := time.Now()

	// 创建内部作废文档数据
	internalDocuments := []mapper.InternalDocumentLibrary{
		{
			ID:             "test_internal_deprecated_doc_001",
			MainID:         "test_internal_deprecated_doc_001", // 主文档的main_id等于其id
			OrganizationID: "test_org_001",
			NoPrefix:       "INT",
			No:             "INT-001",
			SerialNo:       1,
			Name:           "内部作废文档测试1",
			DocCategoryID:  "test_category_001",
			Status:         -1,                    // 作废状态
			PublishDate:    now.AddDate(0, -6, 0), // 6个月前发布
			EffectiveDate:  now.AddDate(0, -5, 0), // 5个月前生效
			CreatedAt:      now.AddDate(0, -6, 0),
			UpdatedAt:      now.AddDate(0, -1, 0), // 1个月前作废
		},
		{
			ID:             "test_internal_deprecated_doc_002",
			MainID:         "test_internal_deprecated_doc_002", // 主文档的main_id等于其id
			OrganizationID: "test_org_001",
			NoPrefix:       "INT",
			No:             "INT-002",
			SerialNo:       2,
			Name:           "内部作废文档测试2",
			DocCategoryID:  "test_category_002",
			Status:         -1,                     // 作废状态
			PublishDate:    now.AddDate(0, -12, 0), // 12个月前发布
			EffectiveDate:  now.AddDate(0, -11, 0), // 11个月前生效
			CreatedAt:      now.AddDate(0, -12, 0),
			UpdatedAt:      now.AddDate(0, -2, 0), // 2个月前作废
		},
		{
			ID:             "test_internal_deprecated_doc_003",
			MainID:         "test_internal_deprecated_doc_003", // 主文档的main_id等于其id
			OrganizationID: "test_org_001",
			NoPrefix:       "QUA",
			No:             "QUA-001",
			SerialNo:       1,
			Name:           "质量管理作废文档",
			DocCategoryID:  "test_category_001",
			Status:         -1,                    // 作废状态
			PublishDate:    now.AddDate(0, -8, 0), // 8个月前发布
			EffectiveDate:  now.AddDate(0, -7, 0), // 7个月前生效
			CreatedAt:      now.AddDate(0, -8, 0),
			UpdatedAt:      now.AddDate(0, -3, 0), // 3个月前作废
		},
		{
			ID:             "test_internal_active_doc_001",
			OrganizationID: "test_org_001",
			NoPrefix:       "INT",
			No:             "INT-003",
			SerialNo:       3,
			Name:           "内部有效文档测试",
			DocCategoryID:  "test_category_001",
			Status:         1, // 有效状态（不应该在作废文档查询中出现）
			PublishDate:    now.AddDate(0, -3, 0),
			EffectiveDate:  now.AddDate(0, -2, 0),
			CreatedAt:      now.AddDate(0, -3, 0),
			UpdatedAt:      now.AddDate(0, -1, 0),
		},
	}

	// 先清理可能存在的重复数据
	for _, doc := range internalDocuments {
		svcCtx.DocvaultDB.GetDB().WithContext(ctx).Unscoped().Where("id = ?", doc.ID).Delete(&mapper.InternalDocumentLibrary{})
	}

	// 批量插入内部文档数据
	for _, doc := range internalDocuments {
		err := svcCtx.DocvaultDB.GetDB().WithContext(ctx).Create(&doc).Error
		if err != nil {
			return nil, err
		}
	}

	// 为每个作废文档创建作废记录（只为status=-1的文档创建）
	for i, doc := range internalDocuments {
		if doc.Status == -1 { // 只为作废状态的文档创建作废记录
			recordID := fmt.Sprintf("test_deprecated_record_%03d", i+1)

			// 创建作废记录
			deprecationRecord := mapper.DeprecationRecord{
				ID:             recordID,
				DeprecateAt:    now.AddDate(0, 1, 0), // 计划1个月后作废
				ApprovalStatus: 3,                    // 已审批
				Reason:         1,                    // 作废原因
				OtherReason:    "",
				WorkflowID:     "test_workflow_001",
				OrganizationID: "test_org_001",
				TenantID:       "test_tenant_001",
				CreatedAt:      now,
				UpdatedAt:      now,
				CreatedBy:      "test_user_001",
				UpdatedBy:      "test_user_001",
			}

			err := svcCtx.DocvaultDB.GetDB().WithContext(ctx).Create(&deprecationRecord).Error
			if err != nil {
				return nil, err
			}

			// 创建作废文档关系
			deprecationRelation := mapper.DeprecationDocumentRelation{
				ID:                  recordID + "_relation",
				DeprecationRecordID: recordID,
				DocumentID:          doc.ID,
			}

			err = svcCtx.DocvaultDB.GetDB().WithContext(ctx).Create(&deprecationRelation).Error
			if err != nil {
				return nil, err
			}
		}
	}

	return &TestData{
		InternalDocuments: internalDocuments,
	}, nil
}

// setupTestDataAndContext 设置测试数据和上下文的通用函数
// 功能：为所有测试函数提供统一的测试环境设置
// 返回值：服务上下文、用户上下文、清理函数、错误信息
func setupTestDataAndContext() (*svc.ServiceContext, context.Context, func(), error) {
	// 设置测试环境
	svcCtx, cleanup, err := setupTestEnvironment()
	if err != nil {
		return nil, nil, nil, err
	}

	// 清理可能存在的测试数据
	cleanupAllTestData(svcCtx)

	// 创建测试数据
	_, err = createTestData(svcCtx)
	if err != nil {
		cleanup()
		return nil, nil, nil, err
	}

	// 创建带有用户信息的上下文
	userInfo := &utils.UserLoginInfo{
		UserId:         "test_user_001",
		TenantId:       "test_tenant_001",
		OrganizationId: "test_org_001",
		DeviceKind:     1,
		IsVirtualUser:  false,
	}
	ctx := userInfo.SetContext(context.Background())

	// 组合清理函数
	combinedCleanup := func() {
		cleanupTestData(svcCtx)
		cleanup()
	}

	return svcCtx, ctx, combinedCleanup, nil
}

// TestInternalDeprecatedDocs_BasicPagination 测试基本分页查询
func TestInternalDeprecatedDocs_BasicPagination(t *testing.T) {
	Convey("测试基本分页查询", t, func() {
		svcCtx, ctx, cleanup, err := setupTestDataAndContext()
		So(err, ShouldBeNil)
		defer cleanup()

		logic := NewGetInternalDeprecatedDocumentsLogic(ctx, svcCtx)

		req := &types.GetInternalDeprecatedDocumentsReq{
			PageInfo: types.PageInfo{
				Page:     1,
				PageSize: 10,
			},
		}

		resp, err := logic.GetInternalDeprecatedDocuments(req)
		So(err, ShouldBeNil)
		So(resp, ShouldNotBeNil)
		So(resp.Total, ShouldEqual, 3) // 只有3个作废状态的文档
		So(len(resp.Data), ShouldEqual, 3)

		// 验证返回的文档ID
		for _, doc := range resp.Data {
			So(doc.ID, ShouldBeIn, []string{
				"test_internal_deprecated_doc_001",
				"test_internal_deprecated_doc_002",
				"test_internal_deprecated_doc_003",
			})
		}
	})
}

// TestInternalDeprecatedDocs_FilterByDocNo 测试按文档编号过滤
func TestInternalDeprecatedDocs_FilterByDocNo(t *testing.T) {
	Convey("测试按文档编号过滤", t, func() {
		svcCtx, ctx, cleanup, err := setupTestDataAndContext()
		So(err, ShouldBeNil)
		defer cleanup()

		logic := NewGetInternalDeprecatedDocumentsLogic(ctx, svcCtx)

		req := &types.GetInternalDeprecatedDocumentsReq{
			PageInfo: types.PageInfo{
				Page:     1,
				PageSize: 10,
			},
			DocumentNo: "INT-001",
		}

		resp, err := logic.GetInternalDeprecatedDocuments(req)
		So(err, ShouldBeNil)
		So(resp, ShouldNotBeNil)
		So(resp.Total, ShouldEqual, 1)
		So(len(resp.Data), ShouldEqual, 1)
		So(resp.Data[0].DocumentNo, ShouldEqual, "INT-001")
		So(resp.Data[0].DocumentName, ShouldEqual, "内部作废文档测试1")
	})
}

// TestInternalDeprecatedDocs_FilterByName 测试按文档名称过滤
func TestInternalDeprecatedDocs_FilterByName(t *testing.T) {
	Convey("测试按文档名称过滤", t, func() {
		svcCtx, ctx, cleanup, err := setupTestDataAndContext()
		So(err, ShouldBeNil)
		defer cleanup()

		logic := NewGetInternalDeprecatedDocumentsLogic(ctx, svcCtx)

		req := &types.GetInternalDeprecatedDocumentsReq{
			PageInfo: types.PageInfo{
				Page:     1,
				PageSize: 10,
			},
			DocumentName: "质量管理",
		}

		resp, err := logic.GetInternalDeprecatedDocuments(req)
		So(err, ShouldBeNil)
		So(resp, ShouldNotBeNil)
		So(resp.Total, ShouldEqual, 1)
		So(len(resp.Data), ShouldEqual, 1)
		So(resp.Data[0].DocumentName, ShouldEqual, "质量管理作废文档")
	})
}

// TestInternalDeprecatedDocs_FilterByOriginalDocNo 测试按原文档编号过滤
func TestInternalDeprecatedDocs_FilterByOriginalDocNo(t *testing.T) {
	Convey("测试按原文档编号过滤", t, func() {
		svcCtx, ctx, cleanup, err := setupTestDataAndContext()
		So(err, ShouldBeNil)
		defer cleanup()

		// 先更新一个文档的原文档编号
		updateCtx := context.Background()
		err = svcCtx.DocvaultDB.GetDB().WithContext(updateCtx).
			Model(&mapper.InternalDocumentLibrary{}).
			Where("id = ?", "test_internal_deprecated_doc_001").
			Update("original_no", "INT-001-V1").Error
		So(err, ShouldBeNil)

		logic := NewGetInternalDeprecatedDocumentsLogic(ctx, svcCtx)

		req := &types.GetInternalDeprecatedDocumentsReq{
			PageInfo: types.PageInfo{
				Page:     1,
				PageSize: 10,
			},
			OriginalDocumentNo: "INT-001-V1",
		}

		resp, err := logic.GetInternalDeprecatedDocuments(req)
		So(err, ShouldBeNil)
		So(resp, ShouldNotBeNil)
		So(resp.Total, ShouldEqual, 1)
		So(len(resp.Data), ShouldEqual, 1)
		So(resp.Data[0].ID, ShouldEqual, "test_internal_deprecated_doc_001")
	})
}

// TestInternalDeprecatedDocs_AttachmentFilter 测试附件过滤功能
func TestInternalDeprecatedDocs_AttachmentFilter(t *testing.T) {
	Convey("测试附件过滤功能", t, func() {
		svcCtx, ctx, cleanup, err := setupTestDataAndContext()
		So(err, ShouldBeNil)
		defer cleanup()

		// 先清空所有文档的附件，确保测试环境干净
		updateCtx := context.Background()
		err = svcCtx.DocvaultDB.GetDB().WithContext(updateCtx).
			Model(&mapper.InternalDocumentLibrary{}).
			Where("id IN ?", []string{"test_internal_deprecated_doc_001", "test_internal_deprecated_doc_002", "test_internal_deprecated_doc_003"}).
			Update("file_id", "").Error
		So(err, ShouldBeNil)

		// 设置第一个文档有附件
		err = svcCtx.DocvaultDB.GetDB().WithContext(updateCtx).
			Model(&mapper.InternalDocumentLibrary{}).
			Where("id = ?", "test_internal_deprecated_doc_001").
			Update("file_id", "test_file_001").Error
		So(err, ShouldBeNil)

		logic := NewGetInternalDeprecatedDocumentsLogic(ctx, svcCtx)

		Convey("有附件过滤", func() {
			req := &types.GetInternalDeprecatedDocumentsReq{
				PageInfo: types.PageInfo{
					Page:     1,
					PageSize: 10,
				},
				HasAttachment: 1, // 有附件
			}

			resp, err := logic.GetInternalDeprecatedDocuments(req)
			So(err, ShouldBeNil)
			So(resp, ShouldNotBeNil)
			// 验证查询不报错（响应结构中没有附件字段）
		})

		Convey("无附件过滤", func() {
			req := &types.GetInternalDeprecatedDocumentsReq{
				PageInfo: types.PageInfo{
					Page:     1,
					PageSize: 10,
				},
				HasAttachment: 2, // 无附件
			}

			resp, err := logic.GetInternalDeprecatedDocuments(req)
			So(err, ShouldBeNil)
			So(resp, ShouldNotBeNil)
			// 验证查询不报错（响应结构中没有附件字段）
		})
	})
}

// TestInternalDeprecatedDocs_FilterByCategory 测试按文档类别过滤
func TestInternalDeprecatedDocs_FilterByCategory(t *testing.T) {
	Convey("测试按文档类别过滤", t, func() {
		svcCtx, ctx, cleanup, err := setupTestDataAndContext()
		So(err, ShouldBeNil)
		defer cleanup()

		logic := NewGetInternalDeprecatedDocumentsLogic(ctx, svcCtx)

		req := &types.GetInternalDeprecatedDocumentsReq{
			PageInfo: types.PageInfo{
				Page:     1,
				PageSize: 10,
			},
			DocumentCategoryID: []string{"test_category_001"},
		}

		resp, err := logic.GetInternalDeprecatedDocuments(req)
		So(err, ShouldBeNil)
		So(resp, ShouldNotBeNil)
		So(resp.Total, ShouldEqual, 2) // test_category_001 有2个作废文档
		So(len(resp.Data), ShouldEqual, 2)
	})
}

// TestInternalDeprecatedDocs_EmptyResult 测试空结果查询
func TestInternalDeprecatedDocs_EmptyResult(t *testing.T) {
	Convey("测试空结果查询", t, func() {
		svcCtx, ctx, cleanup, err := setupTestDataAndContext()
		So(err, ShouldBeNil)
		defer cleanup()

		logic := NewGetInternalDeprecatedDocumentsLogic(ctx, svcCtx)

		req := &types.GetInternalDeprecatedDocumentsReq{
			PageInfo: types.PageInfo{
				Page:     1,
				PageSize: 10,
			},
			DocumentNo: "NONEXISTENT",
		}

		resp, err := logic.GetInternalDeprecatedDocuments(req)
		So(err, ShouldBeNil)
		So(resp, ShouldNotBeNil)
		So(resp.Total, ShouldEqual, 0)
		So(len(resp.Data), ShouldEqual, 0)
	})
}

// TestInternalDeprecatedDocs_CombinedFilters 测试组合条件过滤
func TestInternalDeprecatedDocs_CombinedFilters(t *testing.T) {
	Convey("测试组合条件过滤", t, func() {
		svcCtx, ctx, cleanup, err := setupTestDataAndContext()
		So(err, ShouldBeNil)
		defer cleanup()

		logic := NewGetInternalDeprecatedDocumentsLogic(ctx, svcCtx)

		req := &types.GetInternalDeprecatedDocumentsReq{
			PageInfo: types.PageInfo{
				Page:     1,
				PageSize: 10,
			},
			DocumentName:       "内部作废文档",
			DocumentCategoryID: []string{"test_category_001"},
		}

		resp, err := logic.GetInternalDeprecatedDocuments(req)
		So(err, ShouldBeNil)
		So(resp, ShouldNotBeNil)
		So(resp.Total, ShouldEqual, 1) // 符合所有条件的只有1个
		So(len(resp.Data), ShouldEqual, 1)
		So(resp.Data[0].DocumentName, ShouldEqual, "内部作废文档测试1")
	})
}

// TestGetInternalDeprecatedDocuments_PaginationLogic 测试分页功能
func TestGetInternalDeprecatedDocuments_PaginationLogic(t *testing.T) {
	Convey("测试分页功能", t, func() {
		svcCtx, ctx, cleanup, err := setupTestDataAndContext()
		So(err, ShouldBeNil)
		defer cleanup()

		logic := NewGetInternalDeprecatedDocumentsLogic(ctx, svcCtx)

		Convey("第一页查询", func() {
			req := &types.GetInternalDeprecatedDocumentsReq{
				PageInfo: types.PageInfo{
					Page:     1,
					PageSize: 2,
				},
			}

			resp, err := logic.GetInternalDeprecatedDocuments(req)
			So(err, ShouldBeNil)
			So(resp, ShouldNotBeNil)
			So(resp.Total, ShouldEqual, 3)
			So(len(resp.Data), ShouldEqual, 2)
		})

		Convey("第二页查询", func() {
			req := &types.GetInternalDeprecatedDocumentsReq{
				PageInfo: types.PageInfo{
					Page:     2,
					PageSize: 2,
				},
			}

			resp, err := logic.GetInternalDeprecatedDocuments(req)
			So(err, ShouldBeNil)
			So(resp, ShouldNotBeNil)
			So(resp.Total, ShouldEqual, 3)     // 总共3条记录
			So(len(resp.Data), ShouldEqual, 1) // 第二页1条记录
		})
	})
}

// TestGetInternalDeprecatedDocuments_NoPagination 测试无分页查询
func TestGetInternalDeprecatedDocuments_NoPagination(t *testing.T) {
	Convey("测试无分页查询", t, func() {
		svcCtx, ctx, cleanup, err := setupTestDataAndContext()
		So(err, ShouldBeNil)
		defer cleanup()

		logic := NewGetInternalDeprecatedDocumentsLogic(ctx, svcCtx)

		req := &types.GetInternalDeprecatedDocumentsReq{
			PageInfo: types.PageInfo{
				Page:     1,
				PageSize: 0, // 0表示不分页
			},
		}

		resp, err := logic.GetInternalDeprecatedDocuments(req)
		So(err, ShouldBeNil)
		So(resp, ShouldNotBeNil)
		So(resp.Total, ShouldEqual, 3)
		So(len(resp.Data), ShouldEqual, 3) // 返回所有记录
	})
}

// TestGetInternalDeprecatedDocuments_StatusFilter 测试按状态过滤
func TestGetInternalDeprecatedDocuments_StatusFilter(t *testing.T) {
	Convey("测试按状态过滤", t, func() {
		svcCtx, ctx, cleanup, err := setupTestDataAndContext()
		So(err, ShouldBeNil)
		defer cleanup()

		logic := NewGetInternalDeprecatedDocumentsLogic(ctx, svcCtx)

		req := &types.GetInternalDeprecatedDocumentsReq{
			PageInfo: types.PageInfo{
				Page:     1,
				PageSize: 10,
			},
			Status: -1, // 作废状态
		}

		resp, err := logic.GetInternalDeprecatedDocuments(req)
		So(err, ShouldBeNil)
		So(resp, ShouldNotBeNil)
		So(resp.Total, ShouldEqual, 3) // 3个作废状态的文档
		So(len(resp.Data), ShouldEqual, 3)
	})
}

// TestGetInternalDeprecatedDocuments_ResponseStructure 测试响应数据结构
func TestGetInternalDeprecatedDocuments_ResponseStructure(t *testing.T) {
	Convey("测试响应数据结构", t, func() {
		svcCtx, ctx, cleanup, err := setupTestDataAndContext()
		So(err, ShouldBeNil)
		defer cleanup()

		logic := NewGetInternalDeprecatedDocumentsLogic(ctx, svcCtx)

		req := &types.GetInternalDeprecatedDocumentsReq{
			PageInfo: types.PageInfo{
				Page:     1,
				PageSize: 1,
			},
		}

		resp, err := logic.GetInternalDeprecatedDocuments(req)
		So(err, ShouldBeNil)
		So(resp, ShouldNotBeNil)
		So(len(resp.Data), ShouldEqual, 1)

		// 验证响应数据结构的完整性
		doc := resp.Data[0]
		So(doc.ID, ShouldNotBeEmpty)
		So(doc.DocumentNo, ShouldNotBeEmpty)
		So(doc.DocumentName, ShouldNotBeEmpty)
		// 注意：DocumentCategoryName 可能为空，因为依赖业务字典数据
		// So(doc.DocumentCategoryName, ShouldNotBeEmpty)
		So(doc.FirstPublishDate, ShouldBeGreaterThan, 0)
		So(doc.FirstImplementDate, ShouldBeGreaterThan, 0)
		So(doc.LastDeprecatedDate, ShouldBeGreaterThan, 0)
		So(doc.DeprecatedVersionCount, ShouldBeGreaterThan, 0)
	})
}

// TestGetInternalDeprecatedDocuments_ConcurrentQuery 测试并发查询性能
func TestGetInternalDeprecatedDocuments_ConcurrentQuery(t *testing.T) {
	Convey("测试并发查询性能", t, func() {
		svcCtx, ctx, cleanup, err := setupTestDataAndContext()
		So(err, ShouldBeNil)
		defer cleanup()

		logic := NewGetInternalDeprecatedDocumentsLogic(ctx, svcCtx)

		req := &types.GetInternalDeprecatedDocumentsReq{
			PageInfo: types.PageInfo{
				Page:     1,
				PageSize: 10,
			},
		}

		// 并发查询测试
		type result struct {
			err   error
			total int64
		}
		results := make(chan result, 5)

		for range 5 {
			go func() {
				resp, err := logic.GetInternalDeprecatedDocuments(req)
				if err != nil {
					results <- result{err: err, total: 0}
				} else {
					results <- result{err: nil, total: resp.Total}
				}
			}()
		}

		// 等待所有并发查询完成并验证结果
		for range 5 {
			res := <-results
			So(res.err, ShouldBeNil)
			So(res.total, ShouldEqual, 3)
		}
	})
}

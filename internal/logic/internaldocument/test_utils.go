package internaldocument

import (
	"context"
	"fmt"
	"time"

	"nebula/internal/config"
	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/svc"

	"github.com/zeromicro/go-zero/core/conf"
)

// setupTestEnvironment 设置测试环境
// 功能：初始化测试所需的服务上下文，优化数据库连接池配置
// 返回值：服务上下文，清理函数，错误信息
func setupTestEnvironment() (*svc.ServiceContext, func(), error) {
	// 直接从配置文件初始化依赖
	var c config.Config
	conf.MustLoad("../../../etc/nebula.yaml", &c)

	// 优化测试环境的数据库连接池配置，减少连接数
	optimizeDBConfigForTest(&c)

	svcCtx := &svc.ServiceContext{
		Config:     c,
		DocvaultDB: mapper.NewDocvaultDB(c),
		PhoenixDB:  mapper.NewPhoenixDB(c),
		NebulaDB:   mapper.NewNebulaDB(c),
	}

	cleanup := func() {
		cleanupTestData(svcCtx)
		// 关闭数据库连接，防止连接数过多
		closeDBConnections(svcCtx)
	}
	return svcCtx, cleanup, nil
}

// cleanupTestData 清理测试数据
// 功能：删除测试过程中创建的数据（集成测试数据清理策略）
// 参数：svcCtx - 服务上下文
func cleanupTestData(svcCtx *svc.ServiceContext) {
	ctx := context.Background()

	// 集成测试数据清理策略：
	// 1. 按照外键依赖顺序清理，避免约束冲突
	// 2. 使用事务确保数据一致性
	// 3. 清理所有测试相关的数据

	// 开启事务
	tx := svcCtx.DocvaultDB.GetDB().Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 1. 先清理关联关系表（外键依赖）
	tx.WithContext(ctx).Where("id LIKE ?", "test_%").Delete(&mapper.DeprecationDocumentRelation{})

	// 2. 清理作废记录
	tx.WithContext(ctx).Where("id LIKE ?", "test_%").Delete(&mapper.DeprecationRecord{})

	// 3. 清理文档数据
	tx.WithContext(ctx).Where("id LIKE ?", "test_%").Delete(&mapper.InternalDocumentLibrary{})

	// 4. 清理其他数据库的测试数据
	cleanupCrossDatabaseTestData(svcCtx)

	// 提交事务
	tx.Commit()

	// 清理其他数据库的测试数据
	cleanupCrossDatabaseTestData(svcCtx)
}

// cleanupCrossDatabaseTestData 清理跨数据库的测试数据
// 功能：清理在NebulaDB和PhoenixDB中创建的测试数据（使用SaaS组织架构模型）
// 参数：svcCtx - 服务上下文
func cleanupCrossDatabaseTestData(svcCtx *svc.ServiceContext) {
	ctx := context.Background()

	// 清理NebulaDB中的测试数据
	svcCtx.NebulaDB.GetDB().WithContext(ctx).
		Table("business_dictionary_node_relation").
		Where("node_id LIKE ?", "test_%").
		Delete(nil)

	// 清理PhoenixDB中的SaaS组织架构测试数据
	svcCtx.PhoenixDB.GetDB().WithContext(ctx).
		Where("id LIKE ?", "test_%").
		Delete(&mapper.SaasOrganizationUserInfo{})

	// 清理 saas_organization 表（触发器会自动清理 organization_closure）
	svcCtx.PhoenixDB.GetDB().WithContext(ctx).
		Where("id LIKE ?", "test_%").
		Delete(&mapper.SaasOrganization{})

	svcCtx.PhoenixDB.GetDB().WithContext(ctx).Where("ancestor_id LIKE ? or descendant_id LIKE ?", "test_%", "test_%").
		Unscoped().Delete(&mapper.OrganizationClosure{})

	// 注意：系统实际使用SaaS组织架构，不使用传统的departments和users表
}

// cleanupAllTestData 清理所有测试数据（包括其他可能存在的测试数据）
// 功能：在测试开始前清理可能存在的测试数据
// 参数：svcCtx - 服务上下文
func cleanupAllTestData(svcCtx *svc.ServiceContext) {
	ctx := context.Background()

	// 强制清理所有可能残留的测试数据，不限制状态
	// 1. 先清理关联关系表（外键依赖）
	svcCtx.DocvaultDB.GetDB().WithContext(ctx).
		Unscoped().Where("id LIKE ?", "test_%").
		Delete(&mapper.DeprecationDocumentRelation{})

	// 2. 清理作废记录
	svcCtx.DocvaultDB.GetDB().WithContext(ctx).
		Unscoped().Where("id LIKE ?", "test_%").
		Delete(&mapper.DeprecationRecord{})

	// 3. 清理文档数据（不限制状态和组织ID）
	svcCtx.DocvaultDB.GetDB().WithContext(ctx).
		Unscoped().Where("id LIKE ?", "test_%").
		Delete(&mapper.InternalDocumentLibrary{})

	// 4. 清理其他数据库的测试数据
	cleanupCrossDatabaseTestData(svcCtx)
}

// verifyDatabaseConnections 验证数据库连接
// 功能：集成测试前验证所有数据库连接是否正常
// 参数：svcCtx - 服务上下文
// 返回值：错误信息
func verifyDatabaseConnections(svcCtx *svc.ServiceContext) error {
	ctx := context.Background()

	// 验证DocvaultDB连接
	if err := svcCtx.DocvaultDB.GetDB().WithContext(ctx).Exec("SELECT 1").Error; err != nil {
		return fmt.Errorf("DocvaultDB连接失败: %w", err)
	}

	// 验证NebulaDB连接
	if err := svcCtx.NebulaDB.GetDB().WithContext(ctx).Exec("SELECT 1").Error; err != nil {
		return fmt.Errorf("NebulaDB连接失败: %w", err)
	}

	// 验证PhoenixDB连接
	if err := svcCtx.PhoenixDB.GetDB().WithContext(ctx).Exec("SELECT 1").Error; err != nil {
		return fmt.Errorf("PhoenixDB连接失败: %w", err)
	}

	return nil
}

// closeDBConnections 关闭数据库连接
// 功能：关闭所有数据库连接，防止连接数过多导致测试失败
// 参数：svcCtx - 服务上下文
func closeDBConnections(svcCtx *svc.ServiceContext) {
	// 关闭DocvaultDB连接
	if svcCtx.DocvaultDB != nil {
		if sqlDB, err := svcCtx.DocvaultDB.GetDB().DB(); err == nil {
			sqlDB.Close()
		}
	}

	// 关闭NebulaDB连接
	if svcCtx.NebulaDB != nil {
		if sqlDB, err := svcCtx.NebulaDB.GetDB().DB(); err == nil {
			sqlDB.Close()
		}
	}

	// 关闭PhoenixDB连接
	if svcCtx.PhoenixDB != nil {
		if sqlDB, err := svcCtx.PhoenixDB.GetDB().DB(); err == nil {
			sqlDB.Close()
		}
	}
}

// optimizeDBConfigForTest 优化测试环境的数据库连接池配置
// 功能：减少测试环境中的数据库连接数，防止连接数过多
// 参数：c - 配置对象指针
func optimizeDBConfigForTest(c *config.Config) {
	// 减少最大连接数，适合测试环境
	c.NebulaDB.MaxOpenConns = 5
	c.NebulaDB.MaxIdleConns = 2
	c.NebulaDB.ConnMaxLifetime = 30 // 30分钟

	c.DocvaultDB.MaxOpenConns = 5
	c.DocvaultDB.MaxIdleConns = 2
	c.DocvaultDB.ConnMaxLifetime = 30

	c.PhoenixDB.MaxOpenConns = 5
	c.PhoenixDB.MaxIdleConns = 2
	c.PhoenixDB.ConnMaxLifetime = 30
}

// verifyTestDataIsolation 验证测试数据隔离
// 功能：确保测试数据不会影响生产数据
// 参数：svcCtx - 服务上下文
// 返回值：错误信息
func verifyTestDataIsolation(svcCtx *svc.ServiceContext) error {
	ctx := context.Background()

	// 检查是否有残留的测试数据
	var count int64
	err := svcCtx.DocvaultDB.GetDB().WithContext(ctx).
		Model(&mapper.InternalDocumentLibrary{}).
		Where("id LIKE ?", "test_%").
		Count(&count).Error
	if err != nil {
		return fmt.Errorf("检查测试数据隔离失败: %w", err)
	}

	if count > 0 {
		return fmt.Errorf("发现残留的测试数据，数量: %d", count)
	}

	return nil
}

// measureQueryPerformance 测量查询性能
// 功能：集成测试中测量数据库查询性能
// 参数：operation - 操作名称，fn - 执行函数
// 返回值：执行时间，错误信息
func measureQueryPerformance(operation string, fn func() error) (time.Duration, error) {
	start := time.Now()
	err := fn()
	duration := time.Since(start)

	if err != nil {
		return duration, fmt.Errorf("%s执行失败: %w", operation, err)
	}

	// 记录性能信息（可以用于性能回归测试）
	if duration > 5*time.Second {
		fmt.Printf("⚠️  性能警告: %s 执行时间过长: %v\n", operation, duration)
	}

	return duration, nil
}

package services

import (
	"context"
	"sync"

	"nebula/internal/infrastructure/adapter/mapper"
	"nebula/internal/svc"
	"nebula/internal/utils"
)

// CollectedData 收集的数据结构
// 功能：存储从作废记录中收集的需要查询的数据
type CollectedData struct {
	UserIDs              []string // 用户ID列表
	DeprecationRecordIDs []string // 作废记录ID列表
	CategoryIDs          []string // 文档类别ID列表（内部文档使用）
	TypeIDs              []string // 文档类型ID列表（外部文档使用）
}

// QueryResult 查询结果结构
// 功能：存储并发查询的结果
type QueryResult struct {
	UserNicknames   map[string]string                           // 用户昵称映射
	DocumentCounts  map[string]int32                            // 文档数量统计映射
	DocumentDetails map[string][]mapper.DeprecationDocumentInfo // 文档详情映射
	CategoryNames   map[string]string                           // 文档类别名称映射
}

// InternalDocumentQueryResult 内部文档查询结果结构
// 功能：存储内部文档并发查询的结果
type InternalDocumentQueryResult struct {
	CategoryNames map[string]string // 文档类别名称映射
}

// ExternalDocumentQueryResult 外部文档查询结果结构
// 功能：存储外部文档并发查询的结果
type ExternalDocumentQueryResult struct {
	TypeNames map[string]string // 文档类型名称映射
}

// ConcurrentQueryService 并发查询服务
// 功能：处理用户昵称、文档统计和文档详情的并发查询
type ConcurrentQueryService struct {
	svcCtx *svc.ServiceContext
}

// NewConcurrentQueryService 创建并发查询服务
// 功能：创建并发查询服务实例
// 参数：svcCtx - 服务上下文
// 返回值：并发查询服务实例
func NewConcurrentQueryService(svcCtx *svc.ServiceContext) *ConcurrentQueryService {
	return &ConcurrentQueryService{
		svcCtx: svcCtx,
	}
}

// QueryConcurrently 并发查询用户昵称和文档相关信息
// 功能：使用协程并发查询用户昵称、文档数量统计和文档详情
// 参数：ctx - 上下文，deprecationRecords - 作废记录列表
// 返回值：查询结果，错误信息
// 异常：数据库查询失败、并发执行错误
func (s *ConcurrentQueryService) QueryConcurrently(ctx context.Context, deprecationRecords []mapper.DeprecationRecord) (*QueryResult, error) {
	// 实现步骤：
	// 1. 从作废记录中收集需要查询的数据
	// 2. 启动三个协程分别查询用户昵称、文档数量统计和文档详情
	// 3. 等待所有协程完成
	// 4. 检查错误并返回结果

	// 1. 收集需要查询的数据
	collectedData := s.collectDataFromRecords(deprecationRecords)

	// 2. 并发查询
	var wg sync.WaitGroup
	var userNicknames map[string]string
	var documentCounts map[string]int32
	var documentDetails map[string][]mapper.DeprecationDocumentInfo
	var userErr, documentCountErr, documentDetailsErr error

	// 协程1: 查询用户昵称
	if len(collectedData.UserIDs) > 0 {
		wg.Add(1)
		go func() {
			defer wg.Done()
			userClient := mapper.NewUserClient(s.svcCtx.PhoenixDB)
			userNicknames, userErr = userClient.BatchGetUserNicknames(ctx, collectedData.UserIDs)
		}()
	} else {
		userNicknames = make(map[string]string)
	}

	// 协程2: 查询文档数量统计
	if len(collectedData.DeprecationRecordIDs) > 0 {
		wg.Add(1)
		go func() {
			defer wg.Done()
			deprecationRelationClient := mapper.NewDeprecationDocumentRelationClient(s.svcCtx.DocvaultDB)
			documentCounts, documentCountErr = deprecationRelationClient.BatchGetDocumentCounts(ctx, collectedData.DeprecationRecordIDs)
		}()
	} else {
		documentCounts = make(map[string]int32)
	}

	// 协程3: 查询文档详情
	if len(collectedData.DeprecationRecordIDs) > 0 {
		wg.Add(1)
		go func() {
			defer wg.Done()
			documentDetails, documentDetailsErr = s.batchGetDocumentDetails(ctx, collectedData.DeprecationRecordIDs)
		}()
	} else {
		documentDetails = make(map[string][]mapper.DeprecationDocumentInfo)
	}

	// 3. 等待所有协程完成
	wg.Wait()

	// 4. 检查错误
	if userErr != nil {
		return nil, userErr
	}
	if documentCountErr != nil {
		return nil, documentCountErr
	}
	if documentDetailsErr != nil {
		return nil, documentDetailsErr
	}

	// 5. 从文档详情中提取类别ID并查询类别名称
	categoryIDs := s.extractCategoryIDsFromDocumentDetails(documentDetails)
	var categoryNames map[string]string
	if len(categoryIDs) > 0 {
		var categoryErr error
		categoryNames, categoryErr = s.batchGetCategoryNames(ctx, categoryIDs)
		if categoryErr != nil {
			return nil, categoryErr
		}
	} else {
		categoryNames = make(map[string]string)
	}

	return &QueryResult{
		UserNicknames:   userNicknames,
		DocumentCounts:  documentCounts,
		DocumentDetails: documentDetails,
		CategoryNames:   categoryNames,
	}, nil
}

// collectDataFromRecords 从作废记录中收集需要查询的数据
// 功能：提取作废记录中的用户ID和作废记录ID
// 参数：deprecationRecords - 作废记录列表
// 返回值：收集到的数据结构
func (s *ConcurrentQueryService) collectDataFromRecords(deprecationRecords []mapper.DeprecationRecord) *CollectedData {
	// 实现步骤：
	// 1. 初始化数据收集容器
	// 2. 遍历作废记录，收集用户ID和作废记录ID
	// 3. 去重用户ID
	// 4. 返回收集到的数据

	userIDs := make([]string, 0)
	deprecationRecordIDs := make([]string, 0, len(deprecationRecords))
	userIDSet := make(map[string]bool)

	for _, record := range deprecationRecords {
		// 收集作废记录ID
		deprecationRecordIDs = append(deprecationRecordIDs, record.ID)

		// 收集创建用户ID（去重）
		if !userIDSet[record.CreatedBy] {
			userIDs = append(userIDs, record.CreatedBy)
			userIDSet[record.CreatedBy] = true
		}

		// 收集更新用户ID（去重）
		if record.UpdatedBy != "" && !userIDSet[record.UpdatedBy] {
			userIDs = append(userIDs, record.UpdatedBy)
			userIDSet[record.UpdatedBy] = true
		}

		// 收集审批信息中的用户ID
		s.collectApprovalUserIDs(record.ApprovalInfo, &userIDs, userIDSet)
	}

	return &CollectedData{
		UserIDs:              userIDs,
		DeprecationRecordIDs: deprecationRecordIDs,
	}
}

// collectApprovalUserIDs 收集审批信息中的用户ID
// 功能：解析approval_info字段，提取其中的用户ID
// 参数：approvalInfo - JSON字符串格式的审批信息，userIDs - 用户ID列表指针，userIDSet - 用户ID集合（用于去重）
func (s *ConcurrentQueryService) collectApprovalUserIDs(approvalInfo []byte, userIDs *[]string, userIDSet map[string]bool) {
	if len(approvalInfo) == 0 {
		return
	}
	data, err := utils.NewApprovalParser().ParseApprovalInfoToStruct(string(approvalInfo))
	if err != nil {
		return
	}
	// 收集审核人用户ID
	for _, auditor := range data.Auditors {
		if auditor.UserID != "" && !userIDSet[auditor.UserID] {
			*userIDs = append(*userIDs, auditor.UserID)
			userIDSet[auditor.UserID] = true
		}
	}

	// 收集批准人用户ID
	for _, approver := range data.Approvers {
		if approver.UserID != "" && !userIDSet[approver.UserID] {
			*userIDs = append(*userIDs, approver.UserID)
			userIDSet[approver.UserID] = true
		}
	}
}

// batchGetDocumentDetails 批量获取文档详情（优化版 - 避免循环查库）
// 功能：批量查询作废记录的文档详情，使用批量查询避免N+1问题
// 参数：ctx - 上下文，deprecationRecordIDs - 作废记录ID列表
// 返回值：作废记录ID到文档详情列表的映射，错误信息
func (s *ConcurrentQueryService) batchGetDocumentDetails(ctx context.Context, deprecationRecordIDs []string) (map[string][]mapper.DeprecationDocumentInfo, error) {
	if len(deprecationRecordIDs) == 0 {
		return make(map[string][]mapper.DeprecationDocumentInfo), nil
	}

	// 1. 先批量获取所有文档关系记录
	relationClient := mapper.NewDeprecationDocumentRelationClient(s.svcCtx.DocvaultDB)
	allRelations, err := relationClient.BatchGetByDeprecationRecordIDs(ctx, deprecationRecordIDs)
	if err != nil {
		return nil, err
	}

	if len(allRelations) == 0 {
		// 初始化空结果映射，确保所有记录ID都有对应的空数组
		results := make(map[string][]mapper.DeprecationDocumentInfo)
		for _, recordID := range deprecationRecordIDs {
			results[recordID] = []mapper.DeprecationDocumentInfo{}
		}
		return results, nil
	}

	// 2. 收集所有文档ID并按类型分组
	internalDocIDs := make([]string, 0)
	externalDocIDs := make([]string, 0)

	for _, relation := range allRelations {
		// 根据文档ID前缀或其他规则判断文档类型
		if s.isInternalDocument(relation.DocumentModuleType) {
			internalDocIDs = append(internalDocIDs, relation.DocumentID)
		} else {
			externalDocIDs = append(externalDocIDs, relation.DocumentID)
		}
	}

	// 3. 并行查询内部和外部文档信息
	var wg sync.WaitGroup
	var internalDocs map[string]mapper.DeprecationDocumentInfo
	var externalDocs map[string]mapper.DeprecationDocumentInfo
	var internalErr, externalErr error

	// 查询内部文档
	if len(internalDocIDs) > 0 {
		wg.Add(1)
		go func() {
			defer wg.Done()
			internalDocs, internalErr = s.batchGetInternalDocuments(ctx, internalDocIDs)
		}()
	}

	// 查询外部文档
	if len(externalDocIDs) > 0 {
		wg.Add(1)
		go func() {
			defer wg.Done()
			externalDocs, externalErr = s.batchGetExternalDocuments(ctx, externalDocIDs)
		}()
	}

	wg.Wait()

	// 检查错误
	if internalErr != nil {
		return nil, internalErr
	}
	if externalErr != nil {
		return nil, externalErr
	}

	// 4. 组装结果
	results := make(map[string][]mapper.DeprecationDocumentInfo)

	// 初始化所有记录ID的结果数组
	for _, recordID := range deprecationRecordIDs {
		results[recordID] = []mapper.DeprecationDocumentInfo{}
	}

	// 将文档信息按作废记录ID分组
	for _, relation := range allRelations {
		var docInfo mapper.DeprecationDocumentInfo
		var exists bool

		if s.isInternalDocument(relation.DocumentModuleType) {
			docInfo, exists = internalDocs[relation.DocumentID]
		} else {
			docInfo, exists = externalDocs[relation.DocumentID]
		}

		if exists {
			results[relation.DeprecationRecordID] = append(results[relation.DeprecationRecordID], docInfo)
		}
	}

	return results, nil
}

// QueryUserNicknames 查询用户昵称
// 功能：批量查询用户昵称
// 参数：ctx - 上下文，userIDs - 用户ID列表
// 返回值：用户ID到昵称的映射，错误信息
func (s *ConcurrentQueryService) QueryUserNicknames(ctx context.Context, userIDs []string) (map[string]string, error) {
	if len(userIDs) == 0 {
		return make(map[string]string), nil
	}

	userClient := mapper.NewUserClient(s.svcCtx.PhoenixDB)
	return userClient.BatchGetUserNicknames(ctx, userIDs)
}

// QueryDocumentCounts 查询文档数量统计
// 功能：批量查询作废记录的文档数量统计
// 参数：ctx - 上下文，deprecationRecordIDs - 作废记录ID列表
// 返回值：作废记录ID到文档数量统计的映射，错误信息
func (s *ConcurrentQueryService) QueryDocumentCounts(ctx context.Context, deprecationRecordIDs []string) (map[string]int32, error) {
	if len(deprecationRecordIDs) == 0 {
		return make(map[string]int32), nil
	}

	deprecationRelationClient := mapper.NewDeprecationDocumentRelationClient(s.svcCtx.DocvaultDB)
	return deprecationRelationClient.BatchGetDocumentCounts(ctx, deprecationRecordIDs)
}

// ==================== 内部文档相关方法 ====================

// QueryInternalDocumentsConcurrently 并发查询内部文档类别名称等信息
// 功能：使用协程并发查询内部文档类别名称
// 参数：ctx - 上下文，deprecatedDocuments - 内部文档库列表
// 返回值：查询结果，错误信息
// 异常：数据库查询失败、并发执行错误
func (s *ConcurrentQueryService) QueryInternalDocumentsConcurrently(ctx context.Context, deprecatedDocuments []mapper.InternalDeprecationRecord) (*InternalDocumentQueryResult, error) {
	// 实现步骤：
	// 1. 从内部文档中收集需要查询的数据
	// 2. 启动协程查询文档类别名称
	// 3. 等待协程完成
	// 4. 检查错误并返回结果

	// 1. 收集需要查询的数据
	collectedData := s.collectDataFromInternalDocuments(deprecatedDocuments)

	// 2. 并发查询
	var wg sync.WaitGroup
	var categoryNames map[string]string
	var categoryErr error

	// 协程1: 查询文档类别名称
	if len(collectedData.CategoryIDs) > 0 {
		wg.Add(1)
		go func() {
			defer wg.Done()
			categoryNames, categoryErr = s.batchGetCategoryNames(ctx, collectedData.CategoryIDs)
		}()
	} else {
		categoryNames = make(map[string]string)
	}

	// 3. 等待所有协程完成
	wg.Wait()

	// 4. 检查错误
	if categoryErr != nil {
		return nil, categoryErr
	}

	return &InternalDocumentQueryResult{
		CategoryNames: categoryNames,
	}, nil
}

// QueryInternalDocumentsConcurrentlyForDetail 为内部文档详情查询进行并发查询
// 功能：专门用于详情查询的并发查询，只查询必要的关联数据
// 参数：ctx - 上下文，deprecatedDocuments - 已知的内部文档库列表
// 返回值：内部文档查询结果，错误信息
// 异常：数据库查询失败
// 使用场景：详情查询等已知具体文档列表的场景，日期聚合在组装器中直接计算
func (s *ConcurrentQueryService) QueryInternalDocumentsConcurrentlyForDetail(ctx context.Context, deprecatedDocuments []mapper.InternalDocumentLibrary) (*InternalDocumentQueryResult, error) {
	if len(deprecatedDocuments) == 0 {
		return &InternalDocumentQueryResult{
			CategoryNames: make(map[string]string),
		}, nil
	}

	// 1. 收集需要查询的数据
	collectedData := s.collectDataFromInternalDocumentLibraries(deprecatedDocuments)

	// 2. 查询文档类别名称（日期聚合在组装器中直接计算，无需查询）
	var categoryNames map[string]string
	var categoryErr error

	if len(collectedData.CategoryIDs) > 0 {
		categoryNames, categoryErr = s.batchGetCategoryNames(ctx, collectedData.CategoryIDs)
		if categoryErr != nil {
			return nil, categoryErr
		}
	} else {
		categoryNames = make(map[string]string)
	}

	return &InternalDocumentQueryResult{
		CategoryNames: categoryNames,
	}, nil
}

// collectDataFromInternalDocumentLibraries 从内部文档库列表中收集需要查询的数据
// 功能：提取内部文档库中的文档类别ID
// 参数：deprecatedDocuments - 内部文档库列表
// 返回值：收集到的数据结构
func (s *ConcurrentQueryService) collectDataFromInternalDocumentLibraries(deprecatedDocuments []mapper.InternalDocumentLibrary) *CollectedData {
	categoryIDs := make([]string, 0)
	categoryIDSet := make(map[string]bool)

	for _, document := range deprecatedDocuments {
		// 收集文档类别ID（去重）
		if document.DocCategoryID != "" && !categoryIDSet[document.DocCategoryID] {
			categoryIDs = append(categoryIDs, document.DocCategoryID)
			categoryIDSet[document.DocCategoryID] = true
		}
	}

	return &CollectedData{
		CategoryIDs: categoryIDs,
	}
}

// collectDataFromInternalDocuments 从内部文档中收集需要查询的数据
// 功能：提取内部文档中的文档类别ID
// 参数：deprecatedDocuments - 内部文档库列表
// 返回值：收集到的数据结构
func (s *ConcurrentQueryService) collectDataFromInternalDocuments(deprecatedDocuments []mapper.InternalDeprecationRecord) *CollectedData {
	// 实现步骤：
	// 1. 初始化数据收集容器
	// 2. 遍历内部文档，收集文档类别ID
	// 3. 去重文档类别ID
	// 4. 返回收集到的数据

	categoryIDs := make([]string, 0)
	categoryIDSet := make(map[string]bool)

	for _, document := range deprecatedDocuments {
		// 收集文档类别ID（去重）
		if document.LatestCategoryID != "" && !categoryIDSet[document.LatestCategoryID] {
			categoryIDs = append(categoryIDs, document.LatestCategoryID)
			categoryIDSet[document.LatestCategoryID] = true
		}
	}

	return &CollectedData{
		UserIDs:              []string{}, // 内部文档不需要用户ID
		DeprecationRecordIDs: []string{}, // 内部文档不需要作废记录ID
		CategoryIDs:          categoryIDs,
		TypeIDs:              []string{}, // 内部文档不需要类型ID
	}
}

// ==================== 外部文档相关方法 ====================

// QueryExternalDocumentsConcurrently 并发查询外部文档类型名称等信息
// 功能：使用协程并发查询外部文档类型名称
// 参数：ctx - 上下文，deprecatedDocuments - 外部文档库列表
// 返回值：查询结果，错误信息
// 异常：数据库查询失败、并发执行错误
func (s *ConcurrentQueryService) QueryExternalDocumentsConcurrently(ctx context.Context, deprecatedDocuments []mapper.ExternalDocumentLibrary) (*ExternalDocumentQueryResult, error) {
	// 实现步骤：
	// 1. 从外部文档中收集需要查询的数据
	// 2. 启动协程查询文档类型名称
	// 3. 等待协程完成
	// 4. 检查错误并返回结果

	// 1. 收集需要查询的数据
	collectedData := s.collectDataFromExternalDocuments(deprecatedDocuments)

	// 2. 并发查询
	var wg sync.WaitGroup
	var typeNames map[string]string
	var typeErr error

	// 协程1: 查询文档类型名称
	if len(collectedData.TypeIDs) > 0 {
		wg.Add(1)
		go func() {
			defer wg.Done()
			typeNames, typeErr = s.batchGetTypeNames(ctx, collectedData.TypeIDs)
		}()
	} else {
		typeNames = make(map[string]string)
	}

	// 3. 等待所有协程完成
	wg.Wait()

	// 4. 检查错误
	if typeErr != nil {
		return nil, typeErr
	}

	return &ExternalDocumentQueryResult{
		TypeNames: typeNames,
	}, nil
}

// QueryExternalDeprecationRecordsConcurrently 并发查询外部作废记录类型名称等信息
// 功能：使用协程并发查询外部作废记录类型名称
// 参数：ctx - 上下文，deprecatedDocuments - 外部作废记录列表
// 返回值：查询结果，错误信息
// 异常：数据库查询失败、并发执行错误
func (s *ConcurrentQueryService) QueryExternalDeprecationRecordsConcurrently(ctx context.Context, deprecatedDocuments []mapper.ExternalDeprecationRecord) (*ExternalDocumentQueryResult, error) {
	// 实现步骤：
	// 1. 从外部作废记录中收集需要查询的数据
	// 2. 启动协程查询文档类型名称
	// 3. 等待协程完成
	// 4. 检查错误并返回结果

	// 1. 收集需要查询的数据
	collectedData := s.collectDataFromExternalDeprecationRecords(deprecatedDocuments)

	// 2. 并发查询
	var wg sync.WaitGroup
	var typeNames map[string]string
	var typeErr error

	// 协程1: 查询文档类型名称
	if len(collectedData.TypeIDs) > 0 {
		wg.Add(1)
		go func() {
			defer wg.Done()
			typeNames, typeErr = s.batchGetTypeNames(ctx, collectedData.TypeIDs)
		}()
	} else {
		typeNames = make(map[string]string)
	}

	// 3. 等待所有协程完成
	wg.Wait()

	// 4. 检查错误
	if typeErr != nil {
		return nil, typeErr
	}

	return &ExternalDocumentQueryResult{
		TypeNames: typeNames,
	}, nil
}

// collectDataFromExternalDeprecationRecords 从外部作废记录中收集需要查询的数据
// 功能：提取外部作废记录中的文档类型ID
// 参数：deprecatedDocuments - 外部作废记录列表
// 返回值：收集到的数据结构
func (s *ConcurrentQueryService) collectDataFromExternalDeprecationRecords(deprecatedDocuments []mapper.ExternalDeprecationRecord) *CollectedData {
	typeIDs := make([]string, 0)
	typeIDSet := make(map[string]bool)

	for _, document := range deprecatedDocuments {
		// 收集文档类型ID（去重）
		if document.LatestTypeID != "" && !typeIDSet[document.LatestTypeID] {
			typeIDs = append(typeIDs, document.LatestTypeID)
			typeIDSet[document.LatestTypeID] = true
		}
	}

	return &CollectedData{
		UserIDs:              []string{}, // 外部作废记录不需要用户ID
		DeprecationRecordIDs: []string{}, // 外部作废记录不需要作废记录ID
		CategoryIDs:          []string{}, // 外部作废记录不需要类别ID
		TypeIDs:              typeIDs,
	}
}

// collectDataFromExternalDocuments 从外部文档中收集需要查询的数据
// 功能：提取外部文档中的文档类型ID
// 参数：deprecatedDocuments - 外部文档库列表
// 返回值：收集到的数据结构
func (s *ConcurrentQueryService) collectDataFromExternalDocuments(deprecatedDocuments []mapper.ExternalDocumentLibrary) *CollectedData {
	// 实现步骤：
	// 1. 初始化数据收集容器
	// 2. 遍历外部文档，收集文档类型ID
	// 3. 去重文档类型ID
	// 4. 返回收集到的数据

	typeIDs := make([]string, 0)
	typeIDSet := make(map[string]bool)

	for _, document := range deprecatedDocuments {
		// 收集文档类型ID（去重）
		if document.TypeDictionaryNodeId != "" && !typeIDSet[document.TypeDictionaryNodeId] {
			typeIDs = append(typeIDs, document.TypeDictionaryNodeId)
			typeIDSet[document.TypeDictionaryNodeId] = true
		}
	}

	return &CollectedData{
		UserIDs:              []string{}, // 外部文档不需要用户ID
		DeprecationRecordIDs: []string{}, // 外部文档不需要作废记录ID
		CategoryIDs:          []string{}, // 外部文档不需要类别ID
		TypeIDs:              typeIDs,
	}
}

// isInternalDocument 判断是否为内部文档
// 功能: 根据文档ID判断文档类型
// 参数: documentID - 文档ID
// 返回值: bool - true表示内部文档，false表示外部文档
func (s *ConcurrentQueryService) isInternalDocument(documentModuleType int) bool {
	return documentModuleType == 2
}

// batchGetInternalDocuments 批量获取内部文档信息
// 功能: 批量查询内部文档的详细信息
// 参数: ctx - 上下文，documentIDs - 内部文档ID列表
// 返回值: map[string]DeprecationDocumentInfo - 文档ID到文档信息的映射，错误信息
func (s *ConcurrentQueryService) batchGetInternalDocuments(ctx context.Context, documentIDs []string) (map[string]mapper.DeprecationDocumentInfo, error) {
	if len(documentIDs) == 0 {
		return make(map[string]mapper.DeprecationDocumentInfo), nil
	}

	var internalDocs []struct {
		ID            string `gorm:"column:id"`
		Name          string `gorm:"column:name"`
		No            string `gorm:"column:no"`
		VersionNo     string `gorm:"column:version"`
		Status        int8   `gorm:"column:status"`
		DocCategoryID string `gorm:"column:doc_category_id"`
	}

	// 查询内部文档表
	if err := s.svcCtx.DocvaultDB.GetDB().WithContext(ctx).
		Table("internal_document_library").
		Select("id, name, no, version, status, doc_category_id").
		Where("id IN ?", documentIDs).
		Find(&internalDocs).Error; err != nil {
		return nil, err
	}

	// 转换为DeprecationDocumentInfo格式
	results := make(map[string]mapper.DeprecationDocumentInfo)
	for _, doc := range internalDocs {
		results[doc.ID] = mapper.DeprecationDocumentInfo{
			DocumentID:         doc.ID,
			DocumentName:       doc.Name,
			DocumentNo:         doc.No,
			DocumentVersionNo:  doc.VersionNo,
			DocumentModule:     2, // 内部文档模块类型为2
			DocumentStatus:     int8(doc.Status),
			DocumentCategoryID: doc.DocCategoryID,
		}
	}

	return results, nil
}

// batchGetExternalDocuments 批量获取外部文档信息
// 功能: 批量查询外部文档的详细信息
// 参数: ctx - 上下文，documentIDs - 外部文档ID列表
// 返回值: map[string]DeprecationDocumentInfo - 文档ID到文档信息的映射，错误信息
func (s *ConcurrentQueryService) batchGetExternalDocuments(ctx context.Context, documentIDs []string) (map[string]mapper.DeprecationDocumentInfo, error) {
	if len(documentIDs) == 0 {
		return make(map[string]mapper.DeprecationDocumentInfo), nil
	}

	var externalDocs []struct {
		ID                   string `gorm:"column:id"`
		Name                 string `gorm:"column:name"`
		Number               string `gorm:"column:number"`
		Version              string `gorm:"column:version"`
		Status               int8   `gorm:"column:status"`
		TypeDictionaryNodeId string `gorm:"column:type_dictionary_node_id"`
	}

	// 查询外部文档表
	if err := s.svcCtx.DocvaultDB.GetDB().WithContext(ctx).
		Table("external_document_library").
		Select("id, name, number, version, status, type_dictionary_node_id").
		Where("id IN ?", documentIDs).
		Find(&externalDocs).Error; err != nil {
		return nil, err
	}

	// 转换为DeprecationDocumentInfo格式
	results := make(map[string]mapper.DeprecationDocumentInfo)
	for _, doc := range externalDocs {
		results[doc.ID] = mapper.DeprecationDocumentInfo{
			DocumentID:         doc.ID,
			DocumentName:       doc.Name,
			DocumentNo:         doc.Number,
			DocumentVersionNo:  doc.Version,
			DocumentModule:     3, // 外部文档模块类型为3
			DocumentStatus:     int8(doc.Status),
			DocumentCategoryID: doc.TypeDictionaryNodeId,
		}
	}

	return results, nil
}

// batchGetCategoryNames 批量获取类别名称
// 功能: 批量查询文档类别的名称信息
// 参数: ctx - 上下文，categoryIDs - 类别ID列表
// 返回值: map[string]string - 类别ID到类别名称的映射，错误信息
func (s *ConcurrentQueryService) batchGetCategoryNames(ctx context.Context, categoryIDs []string) (map[string]string, error) {
	if len(categoryIDs) == 0 {
		return make(map[string]string), nil
	}

	// 查询业务字典节点关系
	businessDictClient := mapper.NewBusinessDictionaryNodeRelationClient(s.svcCtx.NebulaDB)
	relations, err := businessDictClient.GetBusinessDictionaryNodeRelationByNodeIDs(ctx, categoryIDs)
	if err != nil {
		return nil, err
	}

	// 构建结果映射
	result := make(map[string]string)
	for _, relation := range relations {
		result[relation.NodeID] = relation.Names
	}

	return result, nil
}

// batchGetTypeNames 批量获取类型名称
// 功能: 批量查询文档类型的名称信息
// 参数: ctx - 上下文，typeIDs - 类型ID列表
// 返回值: map[string]string - 类型ID到类型名称的映射，错误信息
func (s *ConcurrentQueryService) batchGetTypeNames(ctx context.Context, typeIDs []string) (map[string]string, error) {
	if len(typeIDs) == 0 {
		return make(map[string]string), nil
	}

	// 查询业务字典节点关系
	businessDictClient := mapper.NewBusinessDictionaryNodeRelationClient(s.svcCtx.NebulaDB)
	relations, err := businessDictClient.GetBusinessDictionaryNodeRelationByNodeIDs(ctx, typeIDs)
	if err != nil {
		return nil, err
	}

	// 构建结果映射
	result := make(map[string]string)
	for _, relation := range relations {
		result[relation.NodeID] = relation.Names
	}

	return result, nil
}

// extractCategoryIDsFromDocumentDetails 从文档详情中提取类别ID
// 功能: 从文档详情映射中提取所有唯一的文档类别ID
// 参数: documentDetails - 文档详情映射
// 返回值: 类别ID列表
func (s *ConcurrentQueryService) extractCategoryIDsFromDocumentDetails(documentDetails map[string][]mapper.DeprecationDocumentInfo) []string {
	categoryIDSet := make(map[string]bool)

	// 遍历所有文档详情，收集类别ID
	for _, documents := range documentDetails {
		for _, document := range documents {
			if document.DocumentCategoryID != "" {
				categoryIDSet[document.DocumentCategoryID] = true
			}
		}
	}

	// 转换为切片
	categoryIDs := make([]string, 0, len(categoryIDSet))
	for categoryID := range categoryIDSet {
		categoryIDs = append(categoryIDs, categoryID)
	}

	return categoryIDs
}
